/// Mock implementations for MLKit entities used in testing
/// These mocks simulate MLKit behavior without requiring actual ML models

/// Simple mock data structures to simulate MLKit parsing results
class MockMLKitParseResult {
  final String text;
  final int start;
  final int end;
  final String? integerPart;
  final String? fractionalPart;
  final String? currency;
  final DateTime? dateTime;

  MockMLKitParseResult({
    required this.text,
    required this.start,
    required this.end,
    this.integerPart,
    this.fractionalPart,
    this.currency,
    this.dateTime,
  });
}

/// Mock MLKit service for testing purposes
/// This replaces the need to mock the actual MLKit classes
class MockMLKitService {
  List<MockMLKitParseResult> _mockResults = [];
  bool _shouldThrowError = false;
  bool _isInitialized = false;
  bool _modelDownloaded = true;
  
  /// Set the parsing results that this mock should return
  void setMockResults(List<MockMLKitParseResult> results) {
    _mockResults = results;
  }
  
  /// Configure the mock to throw an error when parsing
  void setShouldThrowError(bool shouldThrow) {
    _shouldThrowError = shouldThrow;
  }
  
  /// Configure initialization state
  void setInitialized(bool initialized) {
    _isInitialized = initialized;
  }
  
  /// Configure model download state
  void setModelDownloaded(bool downloaded) {
    _modelDownloaded = downloaded;
  }
  
  /// Simulate MLKit initialization
  Future<bool> initialize() async {
    if (_shouldThrowError) {
      throw Exception('Mock MLKit initialization error');
    }
    _isInitialized = true;
    return _modelDownloaded;
  }
  
  /// Simulate MLKit text annotation
  Future<List<MockMLKitParseResult>> annotateText(String text) async {
    if (!_isInitialized) {
      throw Exception('MLKit not initialized');
    }
    if (_shouldThrowError) {
      throw Exception('Mock MLKit parsing error');
    }
    return _mockResults;
  }
  
  /// Simulate resource cleanup
  Future<void> close() async {
    _isInitialized = false;
  }
  
  /// Check if MLKit is available
  bool get isAvailable => _isInitialized && _modelDownloaded;
}

/// Factory class for creating mock MLKit parse results with common test data
class MockMLKitEntities {
  
  /// Create a mock money parsing result
  static MockMLKitParseResult createMoneyResult({
    required String text,
    required int start,
    required int end,
    String? integerPart,
    String? fractionalPart,
    String? currency,
  }) {
    return MockMLKitParseResult(
      text: text,
      start: start,
      end: end,
      integerPart: integerPart,
      fractionalPart: fractionalPart,
      currency: currency,
    );
  }
  
  /// Create a mock datetime parsing result
  static MockMLKitParseResult createDateTimeResult({
    required String text,
    required int start,
    required int end,
    DateTime? dateTime,
  }) {
    return MockMLKitParseResult(
      text: text,
      start: start,
      end: end,
      dateTime: dateTime,
    );
  }
  
  /// Create common test scenarios
  static List<MockMLKitParseResult> getSimpleExpenseResults() {
    return [
      createMoneyResult(
        text: '\$25.50',
        start: 0,
        end: 6,
        integerPart: '25',
        fractionalPart: '50',
        currency: 'USD',
      ),
    ];
  }
  
  static List<MockMLKitParseResult> getComplexTransactionResults() {
    return [
      createMoneyResult(
        text: '€150.75',
        start: 10,
        end: 17,
        integerPart: '150',
        fractionalPart: '75',
        currency: 'EUR',
      ),
      createDateTimeResult(
        text: 'yesterday',
        start: 20,
        end: 29,
        dateTime: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }
  
  static List<MockMLKitParseResult> getEmptyResults() {
    return [];
  }
  
  /// Create results for various currency formats
  static List<MockMLKitParseResult> getDifferentCurrencyResults() {
    return [
      createMoneyResult(
        text: '¥1000',
        start: 0,
        end: 5,
        integerPart: '1000',
        currency: 'JPY',
      ),
      createMoneyResult(
        text: '£45.99',
        start: 0,
        end: 6,
        integerPart: '45',
        fractionalPart: '99',
        currency: 'GBP',
      ),
      createMoneyResult(
        text: '₹2500.50',
        start: 0,
        end: 8,
        integerPart: '2500',
        fractionalPart: '50',
        currency: 'INR',
      ),
    ];
  }
  
  /// Create results with edge cases
  static List<MockMLKitParseResult> getEdgeCaseResults() {
    return [
      createMoneyResult(
        text: '0.01',
        start: 0,
        end: 4,
        integerPart: '0',
        fractionalPart: '01',
        currency: 'USD',
      ),
      createMoneyResult(
        text: '999999.99',
        start: 0,
        end: 9,
        integerPart: '999999',
        fractionalPart: '99',
        currency: 'USD',
      ),
    ];
  }
}
