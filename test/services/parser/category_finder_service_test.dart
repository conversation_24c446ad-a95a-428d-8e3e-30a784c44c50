import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/category_finder_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../mocks/mock_storage_service.dart';

void main() {
  group('CategoryFinderService Tests', () {
    late CategoryFinderService categoryFinder;
    late MockStorageService mockStorage;

    setUp(() {
      mockStorage = MockStorageService();
      categoryFinder = CategoryFinderService(mockStorage);
    });

    group('Keyword Matching', () {
      test('should find food category for food-related keywords', () async {
        final foodTexts = [
          'coffee at starbucks',
          'lunch at restaurant',
          'dinner with friends',
          'pizza delivery',
          'grocery shopping',
          'mcdonalds burger',
          'breakfast cafe',
        ];

        for (final text in foodTexts) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, equals('food'), reason: 'Failed for: $text');
        }
      });

      test('should find transport category for transport-related keywords', () async {
        final transportTexts = [
          'uber ride',
          'taxi to airport',
          'gas station',
          'parking fee',
          'bus ticket',
          'train fare',
          'flight booking',
          'car rental',
        ];

        for (final text in transportTexts) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, equals('transport'), reason: 'Failed for: $text');
        }
      });

      test('should find shopping category for shopping-related keywords', () async {
        final shoppingTexts = [
          'amazon purchase',
          'clothes shopping',
          'electronics store',
          'walmart groceries',
          'target supplies',
          'best buy laptop',
          'nike shoes',
        ];

        for (final text in shoppingTexts) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, equals('shopping'), reason: 'Failed for: $text');
        }
      });

      test('should handle case insensitive matching', () async {
        final testCases = [
          'COFFEE AT STARBUCKS',
          'Coffee At Starbucks',
          'coffee at starbucks',
          'CoFfEe At StArBuCkS',
        ];

        for (final text in testCases) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, equals('food'), reason: 'Failed for: $text');
        }
      });

      test('should match partial keywords', () async {
        final testCases = [
          'restaurants nearby', // should match 'restaurant' keyword
          'coffees and teas', // should match 'coffee' keyword
          'parking lot', // should match 'parking' keyword
          'shopping mall', // should match 'shopping' keyword
        ];

        for (final text in testCases) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, isNotNull, reason: 'Should find category for: $text');
        }
      });

      test('should return null for unrecognized text', () async {
        final unrecognizedTexts = [
          'random text without keywords',
          'xyz abc def',
          'meeting with client',
          'project work',
        ];

        for (final text in unrecognizedTexts) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, isNull, reason: 'Should not find category for: $text');
        }
      });
    });

    group('Learned Categories', () {
      test('should prioritize learned categories over keywords', () async {
        const text = 'starbucks coffee';
        const learnedCategoryId = 'beverages';
        
        // First, verify keyword matching works
        final keywordResult = await categoryFinder.findCategory(text, TransactionType.expense);
        expect(keywordResult, equals('food')); // Should match food keywords

        // Learn a different category
        await categoryFinder.learnCategory(text, learnedCategoryId);

        // Now it should return the learned category
        final learnedResult = await categoryFinder.findCategory(text, TransactionType.expense);
        expect(learnedResult, equals(learnedCategoryId));
      });

      test('should save and retrieve learned categories', () async {
        const text = 'custom vendor name';
        const categoryId = 'custom-category';

        await categoryFinder.learnCategory(text, categoryId);
        final result = await categoryFinder.findCategory(text, TransactionType.expense);

        expect(result, equals(categoryId));
      });

      test('should handle multiple learned categories', () async {
        final learnedCategories = {
          'my local cafe': 'food',
          'my gym': 'health',
          'my barber': 'personal-care',
          'my dentist': 'health',
        };

        for (final entry in learnedCategories.entries) {
          await categoryFinder.learnCategory(entry.key, entry.value);
        }

        for (final entry in learnedCategories.entries) {
          final result = await categoryFinder.findCategory(entry.key, TransactionType.expense);
          expect(result, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });
    });

    group('Transaction Type Handling', () {
      test('should work with different transaction types', () async {
        const text = 'coffee shop';
        
        final expenseResult = await categoryFinder.findCategory(text, TransactionType.expense);
        final incomeResult = await categoryFinder.findCategory(text, TransactionType.income);
        final loanResult = await categoryFinder.findCategory(text, TransactionType.loan);

        // Currently the category finder doesn't use transaction type in logic,
        // but it should work for all types
        expect(expenseResult, equals('food'));
        expect(incomeResult, equals('food'));
        expect(loanResult, equals('food'));
      });
    });

    group('Edge Cases', () {
      test('should handle empty text', () async {
        final result = await categoryFinder.findCategory('', TransactionType.expense);
        expect(result, isNull);
      });

      test('should handle whitespace-only text', () async {
        final result = await categoryFinder.findCategory('   \n\t  ', TransactionType.expense);
        expect(result, isNull);
      });

      test('should handle very long text', () async {
        final longText = 'A very long description that contains the word coffee somewhere in the middle and continues for a very long time with many other words that should not affect the matching';
        final result = await categoryFinder.findCategory(longText, TransactionType.expense);
        expect(result, equals('food'));
      });

      test('should handle text with special characters', () async {
        final specialTexts = [
          'café & restaurant !@#\$',
          'coffee ☕ shop',
          'gas station ⛽',
          'shopping 🛒 mall',
        ];

        for (final text in specialTexts) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, isNotNull, reason: 'Should find category for: $text');
        }
      });

      test('should handle numbers in text', () async {
        final textsWithNumbers = [
          'coffee shop 123',
          '24/7 gas station',
          'restaurant 2nd floor',
          'shop at 5th avenue',
        ];

        for (final text in textsWithNumbers) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, isNotNull, reason: 'Should find category for: $text');
        }
      });
    });

    group('Data Management', () {
      test('should get all learned categories', () async {
        final testCategories = {
          'vendor1': 'category1',
          'vendor2': 'category2',
          'vendor3': 'category3',
        };

        for (final entry in testCategories.entries) {
          await categoryFinder.learnCategory(entry.key, entry.value);
        }

        final allLearned = await categoryFinder.getAllLearnedCategories();
        expect(allLearned, isA<Map<String, String>>());
        
        for (final entry in testCategories.entries) {
          expect(allLearned.values, contains(entry.value));
        }
      });

      test('should clear learned data', () async {
        await categoryFinder.learnCategory('test vendor', 'test category');
        
        final beforeClear = await categoryFinder.findCategory('test vendor', TransactionType.expense);
        expect(beforeClear, equals('test category'));

        await categoryFinder.clearLearnedData();
        
        final afterClear = await categoryFinder.findCategory('test vendor', TransactionType.expense);
        expect(afterClear, isNull);
      });

      test('should export learned data', () async {
        await categoryFinder.learnCategory('test vendor', 'test category');
        
        final exportedData = await categoryFinder.exportLearnedData();
        expect(exportedData, isNotNull);
        expect(exportedData, isA<String>());
      });

      test('should handle empty learned data gracefully', () async {
        final allLearned = await categoryFinder.getAllLearnedCategories();
        expect(allLearned, isEmpty);

        final exportedData = await categoryFinder.exportLearnedData();
        expect(exportedData, isNull);
      });
    });

    group('Keyword System Information', () {
      test('should get available category IDs', () async {
        final categoryIds = categoryFinder.getAvailableCategoryIds();
        expect(categoryIds, isA<List<String>>());
        expect(categoryIds, isNotEmpty);
        expect(categoryIds, contains('food'));
        expect(categoryIds, contains('transport'));
        expect(categoryIds, contains('shopping'));
      });

      test('should get keywords for specific categories', () async {
        final foodKeywords = categoryFinder.getKeywordsForCategory('food');
        expect(foodKeywords, isA<List<String>>());
        expect(foodKeywords, isNotEmpty);
        expect(foodKeywords, contains('coffee'));
        expect(foodKeywords, contains('restaurant'));

        final transportKeywords = categoryFinder.getKeywordsForCategory('transport');
        expect(transportKeywords, isNotEmpty);
        expect(transportKeywords, contains('taxi'));
        expect(transportKeywords, contains('gas'));
      });

      test('should return empty list for unknown category', () async {
        final unknownKeywords = categoryFinder.getKeywordsForCategory('unknown-category');
        expect(unknownKeywords, isEmpty);
      });
    });

    group('Multilingual Support', () {
      test('should match Spanish keywords', () async {
        final spanishTexts = [
          'comida en restaurante', // food
          'transporte en taxi', // transport
          'compras en tienda', // shopping
        ];

        for (final text in spanishTexts) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, isNotNull, reason: 'Should find category for Spanish: $text');
        }
      });

      test('should match German keywords', () async {
        final germanTexts = [
          'essen im restaurant', // food
          'transport mit taxi', // transport
          'einkaufen im geschäft', // shopping
        ];

        for (final text in germanTexts) {
          final result = await categoryFinder.findCategory(text, TransactionType.expense);
          expect(result, isNotNull, reason: 'Should find category for German: $text');
        }
      });
    });

    group('Brand Name Recognition', () {
      test('should recognize popular brand names', () async {
        final brandTests = {
          'mcdonalds': 'food',
          'starbucks': 'food',
          'amazon': 'shopping',
          'walmart': 'shopping',
          'target': 'shopping',
          'uber': 'transport',
          'nike': 'shopping',
        };

        for (final entry in brandTests.entries) {
          final result = await categoryFinder.findCategory(entry.key, TransactionType.expense);
          expect(result, equals(entry.value), reason: 'Failed for brand: ${entry.key}');
        }
      });
    });

    group('Reported Issues - Keyword Conflict Resolution', () {
      test('should prefer food category for grocery shopping phrases', () async {
        final testCases = {
          'grocery shopping': 'food',
          'food shopping': 'food',
          'supermarket shopping': 'food',
          'restaurant shopping': 'food',
          'cafe shopping': 'food',
        };

        for (final entry in testCases.entries) {
          final result = await categoryFinder.findCategory(entry.key, TransactionType.expense);
          expect(result, equals(entry.value),
                 reason: 'Food category priority failed for: ${entry.key}');
        }
      });

      test('should prefer shopping category for non-food shopping phrases', () async {
        final testCases = {
          'clothes shopping': 'shopping',
          'clothing shopping': 'shopping',
          'electronics shopping': 'shopping',
          'gadget shopping': 'shopping',
          'phone shopping': 'shopping',
          'computer shopping': 'shopping',
          'fashion shopping': 'shopping',
          'shoe shopping': 'shopping',
          'jewelry shopping': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await categoryFinder.findCategory(entry.key, TransactionType.expense);
          expect(result, equals(entry.value),
                 reason: 'Shopping category detection failed for: ${entry.key}');
        }
      });

      test('should handle compound phrases with multiple keywords', () async {
        final testCases = {
          'grocery store shopping trip': 'food',
          'restaurant food shopping': 'food',
          'electronics store shopping mall': 'shopping',
          'clothing store fashion shopping': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await categoryFinder.findCategory(entry.key, TransactionType.expense);
          expect(result, equals(entry.value),
                 reason: 'Compound phrase handling failed for: ${entry.key}');
        }
      });

      test('should apply food-specific keyword bonuses correctly', () async {
        final foodSpecificCases = {
          'grocery': 'food',
          'supermarket': 'food',
          'restaurant': 'food',
          'cafe': 'food',
          'coffee': 'food',
          'meal': 'food',
          'kitchen': 'food',
          'cooking': 'food',
        };

        for (final entry in foodSpecificCases.entries) {
          final result = await categoryFinder.findCategory(entry.key, TransactionType.expense);
          expect(result, equals(entry.value),
                 reason: 'Food-specific keyword bonus failed for: ${entry.key}');
        }
      });

      test('should handle tie-breaking logic correctly', () async {
        // Test the specific case mentioned in the plan
        final result = await categoryFinder.findCategory('grocery shopping', TransactionType.expense);
        expect(result, equals('food'),
               reason: 'Tie-breaking should prefer food for "grocery shopping"');
      });
    });

    group('Performance Tests', () {
      test('should handle repeated lookups efficiently', () async {
        const iterations = 100;
        final testTexts = [
          'coffee shop',
          'gas station',
          'grocery store',
          'restaurant',
          'taxi ride',
        ];

        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < iterations; i++) {
          for (final text in testTexts) {
            await categoryFinder.findCategory(text, TransactionType.expense);
          }
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should complete quickly
      });

      test('should handle large number of learned categories efficiently', () async {
        const numCategories = 100;

        // Learn many categories
        for (int i = 0; i < numCategories; i++) {
          await categoryFinder.learnCategory('vendor$i', 'category$i');
        }

        final stopwatch = Stopwatch()..start();

        // Test lookups
        for (int i = 0; i < numCategories; i++) {
          await categoryFinder.findCategory('vendor$i', TransactionType.expense);
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });
  });
}
