import 'dart:ui';
import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/fallback_parser_service.dart';
import '../../../lib/services/localization_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../../lib/models/localization_data.dart';
import '../../../lib/models/parse_result.dart';
import '../../mocks/mock_storage_service.dart';
import '../../mocks/mock_localization_service.dart';
import '../../test_data/sample_transactions.dart';
import '../../test_data/spanish_sample_transactions.dart';

void main() {
  group('FallbackParserService Tests', () {
    late FallbackParserService fallbackParser;
    late MockStorageService mockStorage;
    late MockLocalizationService mockLocalizationService;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();

      // Set up mock localization service with default English data
      mockLocalizationService = MockLocalizationService();
      mockLocalizationService.setupDefaultEnglishData();

      fallbackParser = FallbackParserService(
        mockStorage,
        localizationService: mockLocalizationService,
      );
    });

    group('Basic Transaction Parsing', () {
      test('should parse simple expense transactions', () async {
        final testCases = SampleTransactions.simpleExpenses;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          
          expect(result.isSuccess, isTrue, reason: 'Failed for: $text');
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should parse income transactions', () async {
        final testCases = SampleTransactions.incomeTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should parse loan transactions', () async {
        final testCases = SampleTransactions.loanTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });
    });

    group('Amount Extraction', () {
      test('should extract amounts with different currency symbols', () async {
        final testCases = {
          '\$25.50': {'amount': 25.50, 'currency': 'USD'},
          '€150.75': {'amount': 150.75, 'currency': 'EUR'},
          '£45.99': {'amount': 45.99, 'currency': 'GBP'},
          '¥1000': {'amount': 1000.0, 'currency': 'JPY'},
          '₹2500.50': {'amount': 2500.50, 'currency': 'INR'},
          '₩5000': {'amount': 5000.0, 'currency': 'KRW'},
        };

        for (final entry in testCases.entries) {
          final text = 'Spent ${entry.key} on shopping';
          final expected = entry.value;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          
          expect(result.transaction.amount, equals(expected['amount']), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expected['currency']), reason: 'Currency mismatch for: $text');
        }
      });

      test('should handle amounts with various formats', () async {
        final testCases = {
          'Spent \$5': 5.0,
          'Paid \$10.99': 10.99,
          'Cost \$100.00': 100.0,
          'Price \$1,234.56': 1234.56,
          'Total \$0.01': 0.01,
          'Amount \$999.99': 999.99,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.amount, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });

      test('should handle whole number amounts', () async {
        final testCases = {
          'Spent \$20': 20.0,
          'Paid €50': 50.0,
          'Cost £100': 100.0,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.amount, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });

      test('should use default currency when none specified', () async {
        await mockStorage.saveDefaultCurrency('EUR');
        
        // Create new parser instance to pick up the default currency
        final parser = FallbackParserService(
          mockStorage,
          localizationService: mockLocalizationService,
        );
        
        const text = 'Spent 25.50 on coffee';
        final result = await parser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.currencyCode, equals('EUR'));
      });
    });

    group('Transaction Type Detection', () {
      test('should detect expense transactions', () async {
        final expenseTexts = [
          'spent \$25 on coffee',
          'paid \$50 for dinner',
          'bought \$30 groceries',
          'cost \$100 for repairs',
          'expense of \$75',
          'charge \$15 for parking',
        ];

        for (final text in expenseTexts) {
          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          expect(result.transaction.type, equals(TransactionType.expense), reason: 'Failed for: $text');
        }
      });

      test('should detect income transactions', () async {
        final incomeTexts = [
          'received \$1000 salary',
          'earned \$500 from freelance',
          'income of \$2000',
          'bonus \$300 from work',
          'refund \$50 from store',
          'cashback \$25',
        ];

        for (final text in incomeTexts) {
          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          expect(result.transaction.type, equals(TransactionType.income), reason: 'Failed for: $text');
        }
      });

      test('should detect loan transactions', () async {
        final loanTexts = [
          'lent \$200 to friend',
          'borrowed \$500 from bank',
          'loan of \$1000',
          'lend \$150 to colleague',
          'borrow \$300 for emergency',
        ];

        for (final text in loanTexts) {
          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          expect(result.transaction.type, equals(TransactionType.loan), reason: 'Failed for: $text');
        }
      });
    });

    group('Currency Detection', () {
      test('should detect currency from symbols', () async {
        final currencyTests = SampleTransactions.currencyVariations;
        
        for (final entry in currencyTests.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedCurrency = testData['expected_currency'] as String;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Failed for: $text');
        }
      });

      test('should handle currency codes in text', () async {
        final testCases = {
          'Spent 25.50 USD on coffee': 'USD',
          'Paid 100 EUR for dinner': 'EUR',
          'Cost 50 GBP for transport': 'GBP',
          'Price 1000 JPY for lunch': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.currencyCode, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });

      test('should handle currency names in text', () async {
        final testCases = {
          'Spent 25 dollars on coffee': 'USD',
          'Paid 100 euros for dinner': 'EUR',
          'Cost 50 pounds for transport': 'GBP',
          'Price 1000 yen for lunch': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.currencyCode, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });
    });

    group('Tag Extraction', () {
      test('should extract hashtags from text', () async {
        final testCases = SampleTransactions.complexTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          if (testData.containsKey('expected_tags')) {
            final text = testData['text'] as String;
            final expectedTags = testData['expected_tags'] as List<dynamic>;

            final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
            
            for (final tag in expectedTags) {
              expect(result.transaction.tags, contains(tag), reason: 'Missing tag $tag for: $text');
            }
          }
        }
      });

      test('should handle multiple hashtags', () async {
        const text = 'Coffee \$5 #morning #coffee #caffeine #daily';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.tags, contains('morning'));
        expect(result.transaction.tags, contains('coffee'));
        expect(result.transaction.tags, contains('caffeine'));
        expect(result.transaction.tags, contains('daily'));
      });

      test('should handle text without hashtags', () async {
        const text = 'Coffee shop \$5.50';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.tags, isEmpty);
      });
    });

    group('Description Generation', () {
      test('should create meaningful descriptions', () async {
        final testCases = {
          'Spent \$25 on coffee at Starbucks': 'coffee at starbucks',
          'Paid €50 for dinner at restaurant': 'dinner at restaurant',
          'Bought \$30 groceries from store': 'groceries from store',
          'Gas station \$40 fill up': 'gas station fill up',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.description.toLowerCase(), contains(entry.value.toLowerCase()), 
                 reason: 'Description mismatch for: ${entry.key}');
        }
      });

      test('should remove hashtags from description', () async {
        const text = 'Coffee \$5 #morning #coffee';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.description, isNot(contains('#morning')));
        expect(result.transaction.description, isNot(contains('#coffee')));
        expect(result.transaction.description.toLowerCase(), contains('coffee'));
      });
    });

    group('Category Integration', () {
      test('should find categories for recognized keywords', () async {
        final testCases = {
          'Coffee \$5 at Starbucks': 'food',
          'Gas \$40 at station': 'transport',
          'Shopping \$100 at mall': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.categoryId, equals(entry.value), reason: 'Category mismatch for: ${entry.key}');
        }
      });

      test('should require category selection when none found', () async {
        const text = 'Random expense \$50 for unknown vendor';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        // Should need category selection when no category is found
        expect(result.requiresUserInput, isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle malformed inputs gracefully', () async {
        final malformedInputs = SampleTransactions.malformedInputs;
        
        for (final entry in malformedInputs.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final shouldFail = testData['should_fail'] as bool? ?? false;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          
          if (shouldFail) {
            expect(result.hasError, isTrue, reason: 'Should fail for: $text');
          } else {
            // If it shouldn't fail, check that we get some reasonable result
            expect(result.transaction.amount, greaterThanOrEqualTo(0.0));
          }
        }
      });

      test('should handle empty text', () async {
        final result = await fallbackParser.parseTransaction('', locale: const Locale('en'));
        expect(result.hasError, isTrue);
        expect(result.error, contains('Could not determine transaction type'));
      });

      test('should handle whitespace-only text', () async {
        final result = await fallbackParser.parseTransaction('   \n\t  ', locale: const Locale('en'));
        expect(result.hasError, isTrue);
      });

      test('should handle text without amounts', () async {
        final result = await fallbackParser.parseTransaction('Went to the store', locale: const Locale('en'));
        expect(result.hasError, isTrue);
        expect(result.error, contains('Could not extract amount'));
      });

      test('should handle text without transaction type indicators', () async {
        final result = await fallbackParser.parseTransaction('\$25.50 something', locale: const Locale('en'));
        expect(result.hasError, isTrue);
        expect(result.error, contains('Could not determine transaction type'));
      });
    });

    group('Edge Cases', () {
      test('should handle very small amounts', () async {
        const text = 'Spent \$0.01 on something';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.amount, equals(0.01));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should handle very large amounts', () async {
        const text = 'Spent \$99999.99 on car';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.amount, equals(99999.99));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should handle zero amounts', () async {
        const text = 'Free coffee \$0.00';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.amount, equals(0.0));
      });

      test('should handle amounts with commas', () async {
        const text = 'Spent \$1,234.56 on laptop';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.amount, equals(1234.56));
      });

      test('should handle special characters in description', () async {
        const text = 'Café & Restaurant! \$25.50 #food';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.description, contains('café'));
        expect(result.transaction.tags, contains('food'));
      });

      test('should handle multiple amounts (should use first)', () async {
        const text = 'Spent \$10 and \$20 on items';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
        
        expect(result.transaction.amount, equals(10.0));
      });
    });

    group('Real-world Examples', () {
      test('should parse real-world transaction examples', () async {
        final realWorldCases = SampleTransactions.realWorldExamples;
        
        for (final entry in realWorldCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          expect(result.isSuccess || result.requiresUserInput, isTrue, reason: 'Should parse successfully: $text');
        }
      });
    });

    group('Reported Issues - Amount Parsing Edge Cases', () {
      test('should parse large whole numbers correctly (not truncated)', () async {
        final testCases = {
          'Spent ¥2500 on dinner': 2500.0,
          'Cost ¥3500 for shopping': 3500.0,
          'Paid ¥10000 for rent': 10000.0,
          'Bought ¥5000 groceries': 5000.0,
          'Expense ¥7500 for travel': 7500.0,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.amount, equals(entry.value),
                 reason: 'Amount parsing failed for: ${entry.key}');
        }
      });

      test('should handle amounts with commas correctly', () async {
        final testCases = {
          'Spent ¥2,500 on dinner': 2500.0,
          'Cost ¥10,000 for rent': 10000.0,
          'Paid ¥1,234.56 for shopping': 1234.56,
          'Expense ¥25,000.99 for car': 25000.99,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.amount, equals(entry.value),
                 reason: 'Comma handling failed for: ${entry.key}');
        }
      });
    });

    group('Reported Issues - Keyword Conflict Resolution', () {
      test('should prefer food category for grocery shopping phrases', () async {
        final testCases = {
          'grocery shopping ¥100': 'food',
          'food shopping ¥50': 'food',
          'supermarket shopping ¥75': 'food',
          'restaurant shopping ¥25': 'food',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.categoryId, equals(entry.value),
                 reason: 'Category conflict resolution failed for: ${entry.key}');
        }
      });

      test('should prefer shopping category for non-food shopping phrases', () async {
        final testCases = {
          'clothes shopping ¥100': 'shopping',
          'electronics shopping ¥500': 'shopping',
          'gadget shopping ¥200': 'shopping',
          'fashion shopping ¥150': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.categoryId, equals(entry.value),
                 reason: 'Shopping category detection failed for: ${entry.key}');
        }
      });
    });

    group('Reported Issues - Currency Context Detection', () {
      test('should detect CNY for Chinese context', () async {
        final testCases = {
          'Beijing restaurant ¥45.50': 'CNY',
          'Shanghai taxi ¥25': 'CNY',
          'Guangzhou shopping ¥100': 'CNY',
          'China trip ¥500': 'CNY',
          'Chinese restaurant ¥80': 'CNY',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.currencyCode, equals(entry.value),
                 reason: 'CNY detection failed for: ${entry.key}');
        }
      });

      test('should detect JPY for Japanese context', () async {
        final testCases = {
          'Tokyo sushi ¥1200': 'JPY',
          'Kyoto temple ¥500': 'JPY',
          'Osaka shopping ¥800': 'JPY',
          'Japan travel ¥2000': 'JPY',
          'Japanese restaurant ¥900': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.currencyCode, equals(entry.value),
                 reason: 'JPY detection failed for: ${entry.key}');
        }
      });
    });

    group('Reported Issues - Negative Number Expense Detection', () {
      test('should detect negative amounts as expenses', () async {
        final testCases = {
          '-¥500 for toys': TransactionType.expense,
          '-\$25.50 coffee': TransactionType.expense,
          '-€100 for shopping': TransactionType.expense,
          '-£75.99 for dinner': TransactionType.expense,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.type, equals(entry.value),
                 reason: 'Negative amount expense detection failed for: ${entry.key}');
          // Amount should be stored as positive value
          expect(result.transaction.amount, greaterThan(0),
                 reason: 'Amount should be positive for: ${entry.key}');
        }
      });

      test('should handle negative amounts with various formats', () async {
        final testCases = {
          '- ¥500 for toys': TransactionType.expense,
          '-\$25.50 coffee shop': TransactionType.expense,
          '- €100.00 for shopping': TransactionType.expense,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('en'));
          expect(result.transaction.type, equals(entry.value),
                 reason: 'Negative amount with spaces failed for: ${entry.key}');
        }
      });
    });

    group('Performance Tests', () {
      test('should handle multiple parsing requests efficiently', () async {
        final testTexts = [
          'Coffee \$5.50 at Starbucks',
          'Gas \$40.00 at station',
          'Groceries \$85.30 at store',
          'Dinner \$45.99 at restaurant',
          'Movie \$12.50 at theater',
        ];

        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 100; i++) {
          for (final text in testTexts) {
            await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          }
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Should complete in reasonable time
      });
    });

    group('Spanish Localization', () {
      late MockLocalizationService spanishMockService;
      late FallbackParserService spanishParser;

      setUp(() async {
        spanishMockService = MockLocalizationService();
        spanishMockService.setupDefaultEnglishData();
        spanishMockService.setupDefaultSpanishData();

        spanishParser = FallbackParserService(
          mockStorage,
          localizationService: spanishMockService,
        );
      });

      test('should parse Spanish expense transactions', () async {
        final testCases = SpanishSampleTransactions.simpleExpenses;

        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await spanishParser.parseTransaction(text, locale: const Locale('es'));

          expect(result.isSuccess, isTrue, reason: 'Failed for: $text');
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should parse Spanish income transactions', () async {
        final testCases = SpanishSampleTransactions.incomeTransactions;

        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await spanishParser.parseTransaction(text, locale: const Locale('es'));

          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should parse Spanish loan transactions', () async {
        final testCases = SpanishSampleTransactions.loanTransactions;

        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await spanishParser.parseTransaction(text, locale: const Locale('es'));

          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should handle Spanish decimal separators', () async {
        const testCases = {
          'Gasté €25,50 en café': 25.50,
          'Recibido €1.500,75 salario': 1500.75,
          'Prestado €100,00 a amigo': 100.00,
        };

        for (final entry in testCases.entries) {
          final result = await spanishParser.parseTransaction(entry.key, locale: const Locale('es'));
          expect(result.transaction.amount, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });

      test('should fallback to English when Spanish locale fails', () async {
        // Test with English text but Spanish locale
        const text = 'Spent \$25.50 on coffee';
        final result = await spanishParser.parseTransaction(text, locale: const Locale('es'));

        // Should still parse successfully using English fallback
        expect(result.isSuccess, isTrue);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.type, equals(TransactionType.expense));
      });
    });

    group('Number Abbreviation Support', () {
      test('should parse basic abbreviations correctly', () async {
        final testCases = [
          {'text': '100k food', 'expectedAmount': 100000.0},
          {'text': '2.5M salary', 'expectedAmount': 2500000.0},
          {'text': '1.2B investment', 'expectedAmount': 1200000000.0},
        ];

        for (final testCase in testCases) {
          final result = await fallbackParser.parseTransaction(testCase['text'] as String);

          // Amount parsing should work correctly regardless of category/type detection
          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing failed for: ${testCase['text']}');

          // Should not fail completely - either success or needs category/type
          expect(result.status != ParseStatus.failed, isTrue,
              reason: 'Parsing should not fail completely for: ${testCase['text']}');
        }
      });

      test('should handle case variations in abbreviations', () async {
        final testCases = [
          {'text': '100K food', 'expectedAmount': 100000.0},
          {'text': '2m salary', 'expectedAmount': 2000000.0},
          {'text': '1.5b investment', 'expectedAmount': 1500000000.0},
        ];

        for (final testCase in testCases) {
          final result = await fallbackParser.parseTransaction(testCase['text'] as String);

          expect(result.isSuccess, isTrue, reason: 'Parsing should succeed for: ${testCase['text']}');
          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Case-insensitive parsing failed for: ${testCase['text']}');
        }
      });

      test('should handle abbreviations with currency symbols', () async {
        final testCases = [
          {'text': '\$100k shopping', 'expectedAmount': 100000.0, 'expectedCurrency': 'USD'},
          {'text': '€2.5M bonus', 'expectedAmount': 2500000.0, 'expectedCurrency': 'EUR'},
          {'text': '£1.5k transport', 'expectedAmount': 1500.0, 'expectedCurrency': 'GBP'},
        ];

        for (final testCase in testCases) {
          final result = await fallbackParser.parseTransaction(testCase['text'] as String);

          expect(result.isSuccess, isTrue, reason: 'Parsing should succeed for: ${testCase['text']}');
          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing with currency failed for: ${testCase['text']}');
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Currency detection failed for: ${testCase['text']}');
        }
      });

      test('should handle abbreviations with currency codes', () async {
        final testCases = [
          {'text': '100k USD shopping', 'expectedAmount': 100000.0, 'expectedCurrency': 'USD'},
          {'text': '2.5M EUR bonus', 'expectedAmount': 2500000.0, 'expectedCurrency': 'EUR'},
          {'text': '1.2B VND investment', 'expectedAmount': 1200000000.0, 'expectedCurrency': 'VND'},
        ];

        for (final testCase in testCases) {
          final result = await fallbackParser.parseTransaction(testCase['text'] as String);

          expect(result.isSuccess, isTrue, reason: 'Parsing should succeed for: ${testCase['text']}');
          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Amount parsing with currency code failed for: ${testCase['text']}');
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Currency code detection failed for: ${testCase['text']}');
        }
      });

      test('should handle thousands separators with abbreviations', () async {
        final testCases = [
          {'text': '1,500k shopping', 'expectedAmount': 1500000.0},
          {'text': '2,500.50M investment', 'expectedAmount': 2500500000.0},
        ];

        for (final testCase in testCases) {
          final result = await fallbackParser.parseTransaction(testCase['text'] as String);

          expect(result.isSuccess, isTrue, reason: 'Parsing should succeed for: ${testCase['text']}');
          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Thousands separator parsing failed for: ${testCase['text']}');
        }
      });

      test('should handle localized separators with abbreviations', () async {
        // Set up Spanish localization with comma as decimal separator
        final spanishMockService = MockLocalizationService();
        spanishMockService.setMockData('es_ES', LocalizationData(
          locale: 'es_ES',
          decimalSeparator: ',',
          thousandsSeparator: '.',
          expenseKeywords: ['gastó', 'pagó', 'compró', 'gasto', 'pago', 'compra'],
          incomeKeywords: ['recibió', 'ganó', 'ingreso', 'salario', 'cobró'],
          loanKeywords: ['prestó', 'pidió prestado', 'préstamo', 'deuda'],
          currencySymbols: ['€', '\$'],
          specialPatterns: {
            'amount': r'(\d+(?:\.\d{3})*(?:,\d{1,2})?)',
            'currency': r'€|EUR|euros?|\$|USD|d[oó]lares?',
          },
        ));

        final spanishParser = FallbackParserService(
          mockStorage,
          localizationService: spanishMockService,
        );

        final testCases = [
          {'text': '100k comida', 'expectedAmount': 100000.0},
          {'text': '2,5M salario', 'expectedAmount': 2500000.0}, // Spanish uses comma as decimal
          {'text': '1.500k compras', 'expectedAmount': 1500000.0}, // Spanish uses dot as thousands
        ];

        for (final testCase in testCases) {
          final result = await spanishParser.parseTransaction(testCase['text'] as String);

          expect(result.isSuccess, isTrue, reason: 'Parsing should succeed for: ${testCase['text']}');
          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Localized separator parsing failed for: ${testCase['text']}');
        }
      });

      test('should maintain backward compatibility with non-abbreviated amounts', () async {
        final testCases = [
          {'text': '\$100.50 food', 'expectedAmount': 100.50},
          {'text': '1,500 euros shopping', 'expectedAmount': 1500.0},
          {'text': '250.75 transport', 'expectedAmount': 250.75},
        ];

        for (final testCase in testCases) {
          final result = await fallbackParser.parseTransaction(testCase['text'] as String);

          expect(result.isSuccess, isTrue, reason: 'Parsing should succeed for: ${testCase['text']}');
          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Backward compatibility failed for: ${testCase['text']}');
        }
      });

      test('should fallback to original regex when AmountUtils fails', () async {
        // Test edge cases where AmountUtils might not find a match but original regex would
        final testCases = [
          {'text': 'spent 50 on lunch', 'expectedAmount': 50.0},
          {'text': 'paid 25.99 for coffee', 'expectedAmount': 25.99},
        ];

        for (final testCase in testCases) {
          final result = await fallbackParser.parseTransaction(testCase['text'] as String);

          expect(result.isSuccess, isTrue, reason: 'Fallback parsing should succeed for: ${testCase['text']}');
          expect(result.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Fallback parsing failed for: ${testCase['text']}');
        }
      });
    });
  });
}
