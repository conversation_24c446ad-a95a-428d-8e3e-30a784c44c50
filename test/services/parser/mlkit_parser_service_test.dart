import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/mlkit_parser_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../../lib/models/parse_result.dart';
import '../../mocks/mock_storage_service.dart';
import '../../test_data/sample_transactions.dart';

void main() {
  group('MlKitParserService Tests', () {
    late MockStorageService mockStorage;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    tearDown(() {
      // Reset singleton instance for clean tests
      MlKitParserService.resetInstance();
    });

    group('Service Initialization', () {
      test('should create singleton instance', () async {
        final instance1 = await MlKitParserService.getInstance(mockStorage);
        final instance2 = await MlKitParserService.getInstance(mockStorage);
        
        expect(identical(instance1, instance2), isTrue);
      });

      test('should initialize with MLKit available', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        expect(service, isNotNull);
      });

      test('should handle MLKit initialization failure gracefully', () async {
        // This test depends on the ability to simulate MLKit initialization failure
        // In a real test, we would mock the MLKit initialization to fail
        final service = await MlKitParserService.getInstance(mockStorage);
        expect(service, isNotNull);
      });
    });

    group('Transaction Parsing with MLKit', () {
      test('should parse simple expense transactions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final testCases = SampleTransactions.simpleExpenses;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await service.parseTransaction(text);
          
          // Should either succeed or fall back gracefully
          expect(result, isNotNull);
          expect(result.transaction.amount, greaterThanOrEqualTo(0.0));
          
          // If parsing is successful, check expected values
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
            expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
          }
        }
      });

      test('should parse income transactions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final testCases = SampleTransactions.incomeTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;

          final result = await service.parseTransaction(text);
          
          expect(result, isNotNull);
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
            expect(result.transaction.type, equals(TransactionType.income), reason: 'Type mismatch for: $text');
          }
        }
      });

      test('should parse complex transactions with tags', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final testCases = SampleTransactions.complexTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedTags = testData['expected_tags'] as List<dynamic>?;

          final result = await service.parseTransaction(text);
          
          expect(result, isNotNull);
          if (expectedTags != null && (result.isSuccess || result.requiresUserInput)) {
            for (final tag in expectedTags) {
              expect(result.transaction.tags, contains(tag), reason: 'Missing tag $tag for: $text');
            }
          }
        }
      });
    });

    group('Fallback Behavior', () {
      test('should fall back to regex parsing when MLKit fails', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        // Test with various transaction texts that should work with fallback
        final testTexts = [
          'Spent \$25.50 on coffee',
          'Received €100 salary',
          'Lent £50 to friend',
        ];

        for (final text in testTexts) {
          final result = await service.parseTransaction(text);
          
          // Should get a result either from MLKit or fallback
          expect(result, isNotNull);
          expect(result.transaction.amount, greaterThan(0.0));
        }
      });

      test('should handle texts that both MLKit and regex can\'t parse', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final malformedInputs = SampleTransactions.malformedInputs;
        
        for (final entry in malformedInputs.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final shouldFail = testData['should_fail'] as bool? ?? false;

          final result = await service.parseTransaction(text);
          
          if (shouldFail) {
            expect(result.hasError, isTrue, reason: 'Should fail for: $text');
          } else {
            // If it shouldn't fail, we should get some result
            expect(result, isNotNull);
          }
        }
      });
    });

    group('Currency Handling', () {
      test('should detect various currency formats', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final currencyTests = SampleTransactions.currencyVariations;
        
        for (final entry in currencyTests.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedCurrency = testData['expected_currency'] as String;

          final result = await service.parseTransaction(text);
          
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          }
        }
      });

      test('should use default currency when none detected', () async {
        await mockStorage.saveDefaultCurrency('EUR');
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Spent 25.50 on something';
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.currencyCode, equals('EUR'));
        }
      });
    });

    group('Category Learning', () {
      test('should learn and apply category associations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee at my local cafe';
        const categoryId = 'food';

        // Learn the association
        await service.learnCategory(text, categoryId);

        // Parse the same text again
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals(categoryId));
        }
      });

      test('should prioritize learned categories over automatic detection', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50';
        const learnedCategoryId = 'beverages';

        // Learn a specific category
        await service.learnCategory(text, learnedCategoryId);

        // Parse again - should use learned category
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals(learnedCategoryId));
        }
      });
    });

    group('Edge Cases', () {
      test('should handle empty input', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('');
        expect(result.hasError, isTrue);
      });

      test('should handle whitespace-only input', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('   \n\t  ');
        expect(result.hasError, isTrue);
      });

      test('should handle very long text', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final longText = 'This is a very long transaction description that contains a lot of text and details about the purchase including the amount of \$25.50 spent on coffee at a local cafe with friends on a sunny afternoon during the weekend when everyone was relaxing and enjoying their time together';
        
        final result = await service.parseTransaction(longText);
        
        // Should handle long text gracefully
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.amount, equals(25.50));
        }
      });

      test('should handle special characters and unicode', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Café & Restaurant! €25.50 🍽️ #food';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.amount, equals(25.50));
          expect(result.transaction.currencyCode, equals('EUR'));
        }
      });

      test('should handle multiple amounts in text', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Spent \$10 on coffee and \$15 on lunch';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          // Should pick one of the amounts (typically the first)
          expect([10.0, 15.0], contains(result.transaction.amount));
        }
      });
    });

    group('Error Handling and Recovery', () {
      test('should handle parsing errors gracefully', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        // Test various problematic inputs
        final problematicInputs = [
          'invalid input with no clear structure',
          '###@@@%%% weird characters',
          'amount without numbers',
          'very confusing transaction description',
        ];

        for (final text in problematicInputs) {
          expect(() async {
            await service.parseTransaction(text);
          }, returnsNormally, reason: 'Should not throw for: $text');
        }
      });

      test('should provide meaningful error messages', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('no amount here');
        
        if (result.hasError) {
          expect(result.error, isNotNull);
          expect(result.error!.length, greaterThan(0));
        }
      });
    });

    group('Transaction ID and Metadata', () {
      test('should generate unique transaction IDs', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final result1 = await service.parseTransaction(text);
        final result2 = await service.parseTransaction(text);
        
        if (result1.isSuccess && result2.isSuccess) {
          expect(result1.transaction.id, isNot(equals(result2.transaction.id)));
        }
      });

      test('should set appropriate timestamps', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final beforeParse = DateTime.now();
        final result = await service.parseTransaction(text);
        final afterParse = DateTime.now();
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.date.isAfter(beforeParse.subtract(const Duration(seconds: 1))), isTrue);
          expect(result.transaction.date.isBefore(afterParse.add(const Duration(seconds: 1))), isTrue);
        }
      });

      test('should create meaningful descriptions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50 downtown location';
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.description, isNotEmpty);
          expect(result.transaction.description.toLowerCase(), contains('coffee'));
        }
      });
    });

    group('Performance Tests', () {
      test('should handle multiple parsing requests efficiently', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final testTexts = [
          'Coffee \$5.50',
          'Gas \$40.00',
          'Groceries \$85.30',
          'Dinner \$45.99',
          'Movie \$12.50',
        ];

        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 50; i++) {
          for (final text in testTexts) {
            await service.parseTransaction(text);
          }
        }

        stopwatch.stop();
        
        // Should complete in reasonable time (this is generous to account for MLKit overhead)
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
      });

      test('should handle singleton access efficiently', () async {
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 100; i++) {
          await MlKitParserService.getInstance(mockStorage);
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });

    group('Reported Issues - Enhanced Fallback Logic', () {
      test('should fallback when MLKit returns empty entities', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // These should trigger fallback due to MLKit limitations
        final testCases = [
          'Spent ¥2500 on dinner',
          'Cost ¥3500 for shopping',
          'Paid ¥10000 for rent',
          'Beijing restaurant ¥45.50',
          'Tokyo sushi ¥1200',
        ];

        for (final text in testCases) {
          final result = await service.parseTransaction(text);

          // Should succeed either via MLKit or fallback
          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should parse successfully: $text');
          expect(result.transaction.amount, greaterThan(0),
                 reason: 'Should extract valid amount: $text');
        }
      });

      test('should fallback when MLKit fails to extract complete information', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          'grocery shopping ¥100',
          'food shopping ¥50',
          'clothes shopping ¥200',
          '-¥500 for toys',
          '- \$25.50 coffee',
        ];

        for (final text in testCases) {
          final result = await service.parseTransaction(text);

          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should handle via fallback: $text');
        }
      });
    });

    group('Reported Issues - Negative Number Handling', () {
      test('should detect negative amounts as expenses', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          '-¥500 for toys': TransactionType.expense,
          '-\$25.50 coffee': TransactionType.expense,
          '-€100 for shopping': TransactionType.expense,
          '-£75.99 for dinner': TransactionType.expense,
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.type, equals(entry.value),
                   reason: 'Negative amount expense detection failed for: ${entry.key}');
            expect(result.transaction.amount, greaterThan(0),
                   reason: 'Amount should be positive for: ${entry.key}');
          }
        }
      });
    });

    group('Reported Issues - Context-Aware Currency Detection', () {
      test('should detect CNY for Chinese context', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'Beijing restaurant ¥45.50': 'CNY',
          'Shanghai taxi ¥25': 'CNY',
          'Guangzhou shopping ¥100': 'CNY',
          'China trip ¥500': 'CNY',
          'Chinese restaurant ¥80': 'CNY',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(entry.value),
                   reason: 'CNY detection failed for: ${entry.key}');
          }
        }
      });

      test('should detect JPY for Japanese context', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'Tokyo sushi ¥1200': 'JPY',
          'Kyoto temple ¥500': 'JPY',
          'Osaka shopping ¥800': 'JPY',
          'Japan travel ¥2000': 'JPY',
          'Japanese restaurant ¥900': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(entry.value),
                   reason: 'JPY detection failed for: ${entry.key}');
          }
        }
      });
    });

    group('Reported Issues - Category Keyword Conflicts', () {
      test('should prefer food category for grocery shopping phrases', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'grocery shopping ¥100': 'food',
          'food shopping ¥50': 'food',
          'supermarket shopping ¥75': 'food',
          'restaurant shopping ¥25': 'food',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals(entry.value),
                   reason: 'Category conflict resolution failed for: ${entry.key}');
          }
        }
      });

      test('should prefer shopping category for non-food shopping phrases', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = {
          'clothes shopping ¥100': 'shopping',
          'electronics shopping ¥500': 'shopping',
          'gadget shopping ¥200': 'shopping',
          'fashion shopping ¥150': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await service.parseTransaction(entry.key);

          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals(entry.value),
                   reason: 'Shopping category detection failed for: ${entry.key}');
          }
        }
      });
    });

    group('Real-world Integration', () {
      test('should parse real-world transaction examples', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final realWorldCases = SampleTransactions.realWorldExamples;

        for (final entry in realWorldCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;

          final result = await service.parseTransaction(text);

          expect(result, isNotNull);

          // Should either succeed or require user input (not fail completely)
          expect(result.isSuccess || result.requiresUserInput, isTrue,
                 reason: 'Should handle real-world case: $text');

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount),
                   reason: 'Amount mismatch for real-world case: $text');
          }
        }
      });
    });

    group('Learning Bypass Functionality', () {
      test('should bypass ML Kit parsing when learned association exists', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'coffee at starbucks';
        const expectedType = TransactionType.expense;
        const expectedCategoryId = 'food';

        // First, learn an association
        await service.learnCategory(text, expectedCategoryId);

        // Parse the same text - should bypass ML Kit and use learned association
        final result = await service.parseTransaction(text);

        expect(result.isSuccess, isTrue);
        expect(result.transaction.type, equals(expectedType));
        expect(result.transaction.categoryId, equals(expectedCategoryId));
        expect(result.transaction.description, equals(text));
      });

      test('should fall back to ML Kit when no learned association exists', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'unknown transaction text';

        // Parse without any learned association
        final result = await service.parseTransaction(text);

        // Should proceed with normal ML Kit parsing
        expect(result, isNotNull);
        // Result can be success, soft fail, or hard fail depending on ML Kit capabilities
      });

      test('should handle learned associations with partial text matching', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const fullText = 'payment to uber technologies inc';
        const partialText = 'uber ride';
        const categoryId = 'transport';

        // Learn with full text
        await service.learnCategory(fullText, categoryId);

        // Should match with partial text
        final result = await service.parseTransaction(partialText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle vendor name extraction in learned associations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const originalText = 'Payment to McDonald\'s Restaurant #1234';
        const testText = 'mcdonalds breakfast';
        const categoryId = 'food';

        // Learn with original text
        await service.learnCategory(originalText, categoryId);

        // Should match with vendor name
        final result = await service.parseTransaction(testText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle case insensitive learned associations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const learnText = 'Starbucks Coffee';
        const testText = 'STARBUCKS COFFEE';
        const categoryId = 'food';

        // Learn with mixed case
        await service.learnCategory(learnText, categoryId);

        // Should match with different case
        final result = await service.parseTransaction(testText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle learning errors gracefully without breaking parsing', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'test transaction';

        // Corrupt the learned associations storage
        await mockStorage.setString('learned_associations', 'invalid json');

        // Parsing should still work even if learning lookup fails
        final result = await service.parseTransaction(text);
        expect(result, isNotNull);
        // Should fall back to normal ML Kit parsing
      });

      test('should prioritize learned associations over ML Kit results', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        const text = 'salary payment \$2000';
        const learnedType = TransactionType.expense; // Intentionally wrong to test priority
        const learnedCategoryId = 'test';

        // Learn an association (even if it contradicts what ML Kit might detect)
        await service.learnCategory(text, learnedCategoryId);

        // Parse - should use learned association, not ML Kit result
        final result = await service.parseTransaction(text);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(learnedCategoryId));
        // The learned association should take priority
      });

      test('should handle multiple learned associations correctly', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': 'starbucks coffee', 'category': 'food'},
          {'text': 'uber ride', 'category': 'transport'},
          {'text': 'amazon purchase', 'category': 'shopping'},
        ];

        // Learn multiple associations
        for (final testCase in testCases) {
          await service.learnCategory(
              testCase['text'] as String,
              testCase['category'] as String);
        }

        // Test each association
        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);
          expect(result.isSuccess, isTrue,
              reason: 'Should find learned association for: ${testCase['text']}');
          expect(result.transaction.categoryId, equals(testCase['category']),
              reason: 'Category mismatch for: ${testCase['text']}');
        }
      });
    });

    group('Soft-Fail Logic Validation', () {
      test('should return needsCategory when no category can be determined', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Use text that has amount and type indicators but no recognizable category
        const unknownText = 'spent 25.50 at qwerty zxcvbn';

        final result = await service.parseTransaction(unknownText);

        // Should either need category selection or succeed with 'unknown' placeholder
        if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.isSuccess) {
          // If it succeeds, it should use 'unknown' placeholder, not 'other'
          expect(result.transaction.categoryId, equals('unknown'));
        } else {
          fail('Unexpected parse result: ${result.status}');
        }
      });

      test('should not default to other category for unknown transactions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        const unknownText = 'spent 42.50 at qwerty zxcvbn';

        final result = await service.parseTransaction(unknownText);

        // Should never return 'other' as category - this was the bug we fixed
        expect(result.transaction.categoryId, isNot(equals('other')));

        // Should either be 'unknown' (placeholder) or require user input
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
        }
      });

      test('should still succeed for learned associations even with soft-fail logic', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        const text = 'spent 30.00 at learned vendor';
        const expectedCategory = 'shopping';

        // Learn an association first
        await service.learnCategory(text, expectedCategory);

        // Parse the same text - should succeed with learned category
        final result = await service.parseTransaction(text);

        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(expectedCategory));
        expect(result.status, equals(ParseStatus.success));
      });

      test('should handle edge cases in soft-fail logic', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          'spent 15.00 at qwerty123',
          'paid 25.50 for zxcvbn',
          'bought asdfgh for 10.00',
        ];

        for (final testText in testCases) {
          final result = await service.parseTransaction(testText);

          // Should never return 'other' category
          expect(result.transaction.categoryId, isNot(equals('other')),
              reason: 'Should not default to other for: $testText');

          // Should either be unknown placeholder or need category selection
          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals('unknown'),
                reason: 'Should use unknown placeholder for: $testText');
          } else {
            expect(result.needsCategorySelection, isTrue,
                reason: 'Should need category selection for: $testText');
          }
        }
      });

      test('should maintain consistency between MlKit and Fallback parser soft-fail behavior', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        // Test text that will likely fall back to regex parser
        const textWithoutMLKitEntities = 'qwerty transaction 50.00';

        final result = await service.parseTransaction(textWithoutMLKitEntities);

        // Regardless of which parser handles it, should not return 'other'
        expect(result.transaction.categoryId, isNot(equals('other')));

        // Should follow same soft-fail logic
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals('unknown'));
        } else if (result.needsCategorySelection) {
          expect(result.status, equals(ParseStatus.needsCategory));
        }
      });

      test('should validate soft-fail logic with various transaction types', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': 'spent 20.00 at qwerty place', 'type': TransactionType.expense},
          {'text': 'received 100.00 from zxcvbn source', 'type': TransactionType.income},
        ];

        for (final testCase in testCases) {
          final result = await service.parseTransaction(testCase['text'] as String);

          // Should detect correct transaction type
          expect(result.transaction.type, equals(testCase['type']),
              reason: 'Type detection failed for: ${testCase['text']}');

          // Should not default to 'other' category
          expect(result.transaction.categoryId, isNot(equals('other')),
              reason: 'Should not default to other for: ${testCase['text']}');
        }
      });
    });
  });
}
