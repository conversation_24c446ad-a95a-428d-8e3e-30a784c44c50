import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:money_lover_chat/screens/chat_screen.dart';
import 'package:money_lover_chat/providers/transaction_provider.dart';
import 'package:money_lover_chat/providers/message_provider.dart';
import 'package:money_lover_chat/services/parser/mlkit_parser_service.dart';
import 'package:money_lover_chat/services/parser/fallback_parser_service.dart';
import 'package:money_lover_chat/models/transaction_model.dart';
import 'package:money_lover_chat/models/parse_result.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Soft Fail Integration Tests', () {
    late TransactionProvider transactionProvider;
    late MessageProvider messageProvider;
    late MLKitParserService mlkitParser;
    late FallbackParserService fallbackParser;

    setUp(() {
      transactionProvider = TransactionProvider();
      messageProvider = MessageProvider();
      mlkitParser = MLKitParserService();
      fallbackParser = FallbackParserService();
    });

    Widget createTestApp() {
      return MultiProvider(
        providers: [
          ChangeNotifierProvider<TransactionProvider>.value(
            value: transactionProvider,
          ),
          ChangeNotifierProvider<MessageProvider>.value(
            value: messageProvider,
          ),
        ],
        child: MaterialApp(
          home: ChatScreen(),
        ),
      );
    }

    group('End-to-End Type Disambiguation Flow', () {
      testWidgets('should complete full type disambiguation flow', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Step 1: User enters ambiguous message
        final textField = find.byType(TextField);
        await tester.enterText(textField, '200k vnd to travel');
        
        // Step 2: Send message
        final sendButton = find.byIcon(Icons.send);
        if (sendButton.evaluate().isNotEmpty) {
          await tester.tap(sendButton);
          await tester.pumpAndSettle();
        }

        // Step 3: Verify user message appears
        expect(find.text('200k vnd to travel'), findsOneWidget);

        // Step 4: Simulate parser returning needsType result
        final partialTransaction = TestHelpers.createTestTransaction(
          amount: 200000,
          currency: 'VND',
          description: 'to travel',
        );
        final parseResult = ParseResult.needsType(partialTransaction);
        
        // Step 5: Verify parse result requires type selection
        expect(parseResult.status, equals(ParseStatus.needsType));
        expect(parseResult.needsTypeSelection, isTrue);

        // Step 6: Simulate system message with quick replies
        final systemMessage = ChatMessage.systemWithQuickReplies(
          id: 'type-selection-${DateTime.now().millisecondsSinceEpoch}',
          text: 'I found an amount of 200,000 VND for travel. What type of transaction is this?',
          timestamp: DateTime.now(),
          quickReplies: ['Expense', 'Income', 'Cancel'],
          quickReplyId: 'type-selection',
        );
        
        messageProvider.addMessage(systemMessage);
        await tester.pumpAndSettle();

        // Step 7: Verify system message and quick replies appear
        expect(find.text('I found an amount of 200,000 VND for travel. What type of transaction is this?'), findsOneWidget);
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);

        // Step 8: User selects transaction type
        await tester.tap(find.text('Expense'));
        await tester.pumpAndSettle();

        // Step 9: Verify transaction is completed
        final completedTransaction = partialTransaction.copyWith(
          type: TransactionType.expense,
        );
        
        transactionProvider.addTransaction(completedTransaction);
        await tester.pumpAndSettle();

        // Step 10: Verify transaction was added
        expect(transactionProvider.transactions.length, equals(1));
        expect(transactionProvider.transactions.first.type, equals(TransactionType.expense));
        expect(transactionProvider.transactions.first.amount, equals(200000));
        expect(transactionProvider.transactions.first.currency, equals('VND'));
      });

      testWidgets('should handle cancel during type selection', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Setup partial transaction
        final partialTransaction = TestHelpers.createTestTransaction(
          amount: 150000,
          currency: 'VND',
        );
        
        transactionProvider.setPendingTransaction(partialTransaction);
        await tester.pumpAndSettle();

        // Add system message with quick replies
        final systemMessage = ChatMessage.systemWithQuickReplies(
          id: 'type-cancel-test',
          text: 'What type of transaction is this?',
          timestamp: DateTime.now(),
          quickReplies: ['Expense', 'Income', 'Cancel'],
          quickReplyId: 'type-selection',
        );
        
        messageProvider.addMessage(systemMessage);
        await tester.pumpAndSettle();

        // User cancels
        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        // Verify pending transaction is cleared
        expect(transactionProvider.pendingTransaction, isNull);
        expect(transactionProvider.transactions.length, equals(0));
      });

      testWidgets('should handle income type selection', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Setup partial transaction
        final partialTransaction = TestHelpers.createTestTransaction(
          amount: 500000,
          currency: 'VND',
          description: 'salary payment',
        );
        
        transactionProvider.setPendingTransaction(partialTransaction);
        await tester.pumpAndSettle();

        // Add system message
        final systemMessage = ChatMessage.systemWithQuickReplies(
          id: 'income-test',
          text: 'What type of transaction is this?',
          timestamp: DateTime.now(),
          quickReplies: ['Expense', 'Income', 'Cancel'],
          quickReplyId: 'type-selection',
        );
        
        messageProvider.addMessage(systemMessage);
        await tester.pumpAndSettle();

        // User selects Income
        await tester.tap(find.text('Income'));
        await tester.pumpAndSettle();

        // Complete transaction
        final completedTransaction = partialTransaction.copyWith(
          type: TransactionType.income,
        );
        
        transactionProvider.completePendingTransaction();
        transactionProvider.addTransaction(completedTransaction);
        await tester.pumpAndSettle();

        // Verify income transaction was created
        expect(transactionProvider.transactions.length, equals(1));
        expect(transactionProvider.transactions.first.type, equals(TransactionType.income));
        expect(transactionProvider.pendingTransaction, isNull);
      });
    });

    group('End-to-End Category Disambiguation Flow', () {
      testWidgets('should complete full category disambiguation flow', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Step 1: Create transaction that needs category
        final partialTransaction = TestHelpers.createTestTransaction(
          amount: 50000,
          currency: 'VND',
          type: TransactionType.expense,
          description: 'lunch',
        );
        
        transactionProvider.setPendingTransaction(partialTransaction);
        await tester.pumpAndSettle();

        // Step 2: Simulate needsCategory parse result
        final parseResult = ParseResult.needsCategory(partialTransaction);
        expect(parseResult.status, equals(ParseStatus.needsCategory));
        expect(parseResult.needsCategorySelection, isTrue);

        // Step 3: Add system message for category selection
        final systemMessage = ChatMessage.systemWithQuickReplies(
          id: 'category-selection',
          text: 'I found an expense of 50,000 VND for lunch. Which category should this go in?',
          timestamp: DateTime.now(),
          quickReplies: ['Food', 'Transport', 'Shopping', 'Other', 'Cancel'],
          quickReplyId: 'category-selection',
        );
        
        messageProvider.addMessage(systemMessage);
        await tester.pumpAndSettle();

        // Step 4: Verify category options appear
        expect(find.text('Food'), findsOneWidget);
        expect(find.text('Transport'), findsOneWidget);
        expect(find.text('Shopping'), findsOneWidget);
        expect(find.text('Other'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);

        // Step 5: User selects category
        await tester.tap(find.text('Food'));
        await tester.pumpAndSettle();

        // Step 6: Complete transaction with category
        final completedTransaction = partialTransaction.copyWith(
          category: TestHelpers.createTestCategory(name: 'Food'),
        );
        
        transactionProvider.completePendingTransaction();
        transactionProvider.addTransaction(completedTransaction);
        await tester.pumpAndSettle();

        // Step 7: Verify transaction was completed
        expect(transactionProvider.transactions.length, equals(1));
        expect(transactionProvider.transactions.first.category?.name, equals('Food'));
        expect(transactionProvider.pendingTransaction, isNull);
      });

      testWidgets('should handle learning confirmation after category selection', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Setup completed transaction
        final transaction = TestHelpers.createTestTransaction(
          description: 'coffee shop',
          category: TestHelpers.createTestCategory(name: 'Food'),
        );
        
        transactionProvider.addTransaction(transaction);
        await tester.pumpAndSettle();

        // Add learning confirmation message
        final learningMessage = ChatMessage.systemWithQuickReplies(
          id: 'learning-confirmation',
          text: 'Should I remember that "coffee shop" transactions should be categorized as Food for future transactions?',
          timestamp: DateTime.now(),
          quickReplies: ['Yes', 'No'],
          quickReplyId: 'learning-confirmation',
        );
        
        messageProvider.addMessage(learningMessage);
        await tester.pumpAndSettle();

        // Verify learning confirmation appears
        expect(find.text('Should I remember that "coffee shop" transactions should be categorized as Food for future transactions?'), findsOneWidget);
        expect(find.text('Yes'), findsOneWidget);
        expect(find.text('No'), findsOneWidget);

        // User confirms learning
        await tester.tap(find.text('Yes'));
        await tester.pumpAndSettle();

        // Verify learning was accepted (this would typically update a learning service)
        expect(find.text('Yes'), findsOneWidget);
      });

      testWidgets('should handle "Other" category selection', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Setup partial transaction
        final partialTransaction = TestHelpers.createTestTransaction(
          description: 'unusual expense',
        );
        
        transactionProvider.setPendingTransaction(partialTransaction);
        await tester.pumpAndSettle();

        // Add category selection message
        final systemMessage = ChatMessage.systemWithQuickReplies(
          id: 'other-category-test',
          text: 'Which category should this go in?',
          timestamp: DateTime.now(),
          quickReplies: ['Food', 'Transport', 'Other', 'Cancel'],
          quickReplyId: 'category-selection',
        );
        
        messageProvider.addMessage(systemMessage);
        await tester.pumpAndSettle();

        // User selects "Other"
        await tester.tap(find.text('Other'));
        await tester.pumpAndSettle();

        // This would typically prompt for custom category input
        // For now, just verify the selection was processed
        expect(find.text('Other'), findsOneWidget);
      });
    });

    group('Parser Integration Tests', () {
      testWidgets('should handle MLKit parser failure gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Simulate MLKit parser failure
        const testInput = 'complex transaction text that MLKit cannot parse';
        
        // This would typically be handled by the parser service
        // For testing, we simulate the fallback scenario
        final fallbackResult = await fallbackParser.parseTransaction(testInput);
        
        // Verify fallback parser was used
        expect(fallbackResult, isNotNull);
        
        // If fallback also fails, should return failed status
        if (fallbackResult.status == ParseStatus.failed) {
          expect(fallbackResult.error, isNotNull);
        }
      });

      testWidgets('should handle partial MLKit success with type disambiguation', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Simulate MLKit extracting amount but not type
        const testInput = '100k vnd for something';
        
        // This would be the actual MLKit result
        final partialTransaction = TestHelpers.createTestTransaction(
          amount: 100000,
          currency: 'VND',
          description: 'for something',
        );
        
        final parseResult = ParseResult.needsType(partialTransaction);
        
        // Verify the result requires type selection
        expect(parseResult.status, equals(ParseStatus.needsType));
        expect(parseResult.transaction.amount, equals(100000));
        expect(parseResult.transaction.currency, equals('VND'));
      });

      testWidgets('should handle successful parsing without soft fail', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Simulate successful parsing
        const testInput = 'spent 50k vnd on food';
        
        final completeTransaction = TestHelpers.createTestTransaction(
          amount: 50000,
          currency: 'VND',
          type: TransactionType.expense,
          description: 'food',
          category: TestHelpers.createTestCategory(name: 'Food'),
        );
        
        final parseResult = ParseResult.success(completeTransaction);
        
        // Verify successful parsing
        expect(parseResult.status, equals(ParseStatus.success));
        expect(parseResult.isSuccess, isTrue);
        expect(parseResult.requiresUserInput, isFalse);
        
        // Add transaction directly
        transactionProvider.addTransaction(completeTransaction);
        await tester.pumpAndSettle();
        
        // Verify transaction was added without soft fail
        expect(transactionProvider.transactions.length, equals(1));
        expect(transactionProvider.pendingTransaction, isNull);
      });
    });

    group('Error Recovery Tests', () {
      testWidgets('should recover from parsing errors', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Simulate parsing error
        final parseResult = ParseResult.failed('Unable to extract transaction details');
        
        expect(parseResult.status, equals(ParseStatus.failed));
        expect(parseResult.error, equals('Unable to extract transaction details'));
        expect(parseResult.isSuccess, isFalse);

        // User should be able to try again with different input
        final textField = find.byType(TextField);
        await tester.enterText(textField, 'spent 30k vnd on coffee');
        
        // This should work on retry
        final retryTransaction = TestHelpers.createTestTransaction(
          amount: 30000,
          currency: 'VND',
          description: 'coffee',
        );
        
        final retryResult = ParseResult.success(retryTransaction);
        expect(retryResult.isSuccess, isTrue);
      });

      testWidgets('should handle rapid user interactions gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Add multiple quick reply messages rapidly
        for (int i = 0; i < 5; i++) {
          final message = ChatMessage.systemWithQuickReplies(
            id: 'rapid-test-$i',
            text: 'Quick test $i',
            timestamp: DateTime.now().add(Duration(milliseconds: i)),
            quickReplies: ['Option A', 'Option B'],
            quickReplyId: 'rapid-test-$i',
          );
          
          messageProvider.addMessage(message);
        }
        
        await tester.pumpAndSettle();

        // Rapidly tap multiple options
        final optionAFinders = find.text('Option A');
        for (int i = 0; i < optionAFinders.evaluate().length && i < 3; i++) {
          await tester.tap(optionAFinders.at(i));
          await tester.pump();
        }

        // Should handle without errors
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle malformed quick reply data', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Create message with problematic quick replies
        final message = ChatMessage.systemWithQuickReplies(
          id: 'malformed-test',
          text: 'Test message',
          timestamp: DateTime.now(),
          quickReplies: [], // Empty quick replies
          quickReplyId: 'malformed-test',
        );
        
        messageProvider.addMessage(message);
        await tester.pumpAndSettle();

        // Should handle gracefully
        expect(find.text('Test message'), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle network or service failures', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Simulate service failure scenario
        try {
          // This would typically involve mocking service failures
          final parseResult = ParseResult.failed('Network error: Unable to connect to parsing service');
          
          expect(parseResult.status, equals(ParseStatus.failed));
          expect(parseResult.error, contains('Network error'));
          
          // App should continue to function
          final textField = find.byType(TextField);
          await tester.enterText(textField, 'test message');
          
          expect(find.text('test message'), findsOneWidget);
        } catch (e) {
          // Should not crash the app
          expect(tester.takeException(), isNull);
        }
      });
    });

    group('State Management Integration', () {
      testWidgets('should maintain consistent state across providers', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Test transaction provider state
        final transaction = TestHelpers.createTestTransaction();
        transactionProvider.addTransaction(transaction);
        await tester.pumpAndSettle();

        expect(transactionProvider.transactions.length, equals(1));
        expect(transactionProvider.transactions.first.id, equals(transaction.id));

        // Test message provider state
        final message = ChatMessage.user(
          id: 'test-message',
          text: 'Test message',
          timestamp: DateTime.now(),
        );
        
        messageProvider.addMessage(message);
        await tester.pumpAndSettle();

        expect(messageProvider.messages.length, equals(1));
        expect(messageProvider.messages.first.text, equals('Test message'));

        // Test pending transaction state
        final pendingTransaction = TestHelpers.createTestTransaction();
        transactionProvider.setPendingTransaction(pendingTransaction);
        await tester.pumpAndSettle();

        expect(transactionProvider.pendingTransaction, isNotNull);
        expect(transactionProvider.pendingTransaction!.id, equals(pendingTransaction.id));

        // Clear pending state
        transactionProvider.completePendingTransaction();
        await tester.pumpAndSettle();

        expect(transactionProvider.pendingTransaction, isNull);
      });

      testWidgets('should handle concurrent state updates', (WidgetTester tester) async {
        await tester.pumpWidget(createTestApp());

        // Simulate concurrent updates
        final transaction1 = TestHelpers.createTestTransaction();
        final transaction2 = TestHelpers.createTestTransaction();
        final message1 = ChatMessage.user(
          id: 'msg1',
          text: 'Message 1',
          timestamp: DateTime.now(),
        );
        final message2 = ChatMessage.system(
          id: 'msg2',
          text: 'Message 2',
          timestamp: DateTime.now(),
        );

        // Add items concurrently
        transactionProvider.addTransaction(transaction1);
        messageProvider.addMessage(message1);
        transactionProvider.addTransaction(transaction2);
        messageProvider.addMessage(message2);
        
        await tester.pumpAndSettle();

        // Verify all items were added correctly
        expect(transactionProvider.transactions.length, equals(2));
        expect(messageProvider.messages.length, equals(2));
        
        // Verify order is maintained
        expect(messageProvider.messages[0].text, equals('Message 1'));
        expect(messageProvider.messages[1].text, equals('Message 2'));
      });
    });
  });
}
