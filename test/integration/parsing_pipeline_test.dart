import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/mlkit_parser_service.dart';
import '../../lib/services/parser/fallback_parser_service.dart';
import '../../lib/models/transaction_model.dart';
import '../mocks/mock_storage_service.dart';
import '../test_data/sample_transactions.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Parsing Pipeline Integration Tests', () {
    late MockStorageService mockStorage;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    group('End-to-End Parsing with MLKit Primary', () {
      test('should handle complete parsing workflow', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        
        // Test various transaction scenarios
        final testScenarios = [
          {
            'text': 'Coffee at Starbucks \$5.50 #morning',
            'expected_type': TransactionType.expense,
            'expected_amount': 5.50,
            'expected_currency': 'USD',
            'expected_tags': ['morning'],
          },
          {
            'text': 'Salary payment €2500.00',
            'expected_type': TransactionType.income,
            'expected_amount': 2500.00,
            'expected_currency': 'EUR',
          },
          {
            'text': 'Lent £200 to friend',
            'expected_type': TransactionType.loan,
            'expected_amount': 200.00,
            'expected_currency': 'GBP',
          },
        ];

        for (final scenario in testScenarios) {
          final text = scenario['text'] as String;
          final result = await mlkitService.parseTransaction(text);
          
          expect(result, isNotNull, reason: 'Should parse: $text');
          expect(result.isSuccess || result.requiresUserInput, isTrue, 
                 reason: 'Should succeed or need category for: $text');
          
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(scenario['expected_amount']), 
                   reason: 'Amount mismatch for: $text');
            expect(result.transaction.type, equals(scenario['expected_type']), 
                   reason: 'Type mismatch for: $text');
            expect(result.transaction.currencyCode, equals(scenario['expected_currency']), 
                   reason: 'Currency mismatch for: $text');
            
            if (scenario.containsKey('expected_tags')) {
              final expectedTags = scenario['expected_tags'] as List<String>;
              for (final tag in expectedTags) {
                expect(result.transaction.tags, contains(tag), 
                       reason: 'Missing tag $tag for: $text');
              }
            }
          }
        }
      });

      test('should handle category learning and application', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        
        const vendorText = 'My local coffee shop \$4.50';
        const categoryId = 'local-food';

        // First parse - should need category selection
        await mlkitService.parseTransaction(vendorText);
        
        // Learn the category
        await mlkitService.learnCategory(vendorText, categoryId);

        // Second parse - should use learned category
        final secondResult = await mlkitService.parseTransaction(vendorText);
        
        if (secondResult.isSuccess) {
          expect(secondResult.transaction.categoryId, equals(categoryId));
        }
      });
    });

    group('Fallback Parser Integration', () {
      test('should provide consistent results with regex parsing', () async {
        final fallbackService = FallbackParserService(mockStorage);
        
        final testCases = SampleTransactions.simpleExpenses;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackService.parseTransaction(text);
          
          expect(result, isNotNull);
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount), 
                   reason: 'Fallback amount mismatch for: $text');
            expect(result.transaction.type, equals(expectedType), 
                   reason: 'Fallback type mismatch for: $text');
          }
        }
      });

      test('should handle edge cases gracefully', () async {
        final fallbackService = FallbackParserService(mockStorage);
        
        final edgeCases = [
          'Very complex transaction with \$25.50 and lots of details',
          'Simple: \$10',
          'Café ☕ €5.00 #coffee',
          'Gas ⛽ £40.99 fill-up',
        ];

        for (final text in edgeCases) {
          final result = await fallbackService.parseTransaction(text);
          expect(result, isNotNull, reason: 'Should handle edge case: $text');
        }
      });
    });

    group('Performance and Memory Tests', () {
      test('should handle high-volume parsing efficiently', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);
        
        final testTexts = TestHelpers.getTestTransactionTexts();
        const iterations = 50;
        
        final stopwatch = Stopwatch()..start();
        
        // Test MLKit service
        for (int i = 0; i < iterations; i++) {
          for (final text in testTexts) {
            await mlkitService.parseTransaction(text);
          }
        }
        
        final mlkitTime = stopwatch.elapsedMilliseconds;
        stopwatch.reset();
        
        // Test fallback service
        for (int i = 0; i < iterations; i++) {
          for (final text in testTexts) {
            await fallbackService.parseTransaction(text);
          }
        }
        
        final fallbackTime = stopwatch.elapsedMilliseconds;
        stopwatch.stop();
        
        print('MLKit service: ${mlkitTime}ms, Fallback service: ${fallbackTime}ms');
        
        // Both should complete in reasonable time
        expect(mlkitTime, lessThan(15000)); // 15 seconds max for MLKit
        expect(fallbackTime, lessThan(5000));  // 5 seconds max for fallback
      });

      test('should manage memory efficiently with repeated parsing', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50 at local cafe';
        const iterations = 100;
        
        // Parse the same text many times
        for (int i = 0; i < iterations; i++) {
          final result = await service.parseTransaction(text);
          expect(result, isNotNull);
        }
        
        // If we get here without memory issues, the test passes
        expect(true, isTrue);
      });
    });

    group('Real-world Scenario Testing', () {
      test('should parse comprehensive real-world examples', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final realWorldCases = SampleTransactions.realWorldExamples;
        
        for (final entry in realWorldCases.entries) {
          final testName = entry.key;
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;

          final result = await service.parseTransaction(text);
          
          expect(result, isNotNull, reason: 'Real-world case failed: $testName');
          expect(result.isSuccess || result.requiresUserInput, isTrue, 
                 reason: 'Should handle real-world case: $testName');
          
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount), 
                   reason: 'Amount mismatch for real-world case: $testName');
            expect(result.transaction.currencyCode, equals(expectedCurrency), 
                   reason: 'Currency mismatch for real-world case: $testName');
          }
        }
      });

      test('should handle mixed-language transactions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final multilingualCases = [
          'Café lunch €12.50 #déjeuner',
          'Sushi 🍣 ¥1200 delicious',
          'Taxi fare ₹150 to airport',
          'Cerveza 🍺 \$5.00 with amigos',
        ];

        for (final text in multilingualCases) {
          final result = await service.parseTransaction(text);
          
          expect(result, isNotNull, reason: 'Should handle multilingual: $text');
          expect(result.transaction.amount, greaterThan(0.0), 
                 reason: 'Should extract amount from: $text');
        }
      });
    });

    group('Error Recovery and Resilience', () {
      test('should recover from various parsing failures', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final problematicInputs = [
          '', // Empty
          '   ', // Whitespace
          'No amount here', // No amount
          'Invalid currency XYZ 123', // Invalid currency
          'Multiple \$10 amounts \$20 here', // Multiple amounts
          '🎉🎉🎉 \$5.50 🎉🎉🎉', // Lots of emojis
        ];

        for (final text in problematicInputs) {
          expect(() async {
            await service.parseTransaction(text);
          }, returnsNormally, reason: 'Should not throw for: $text');
        }
      });

      test('should provide useful error information', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('invalid transaction text');
        
        if (result.hasError) {
          expect(result.error, isNotNull);
          expect(result.error!.length, greaterThan(0));
          // Error should be descriptive
          expect(result.error!.toLowerCase(), anyOf(
            contains('amount'),
            contains('type'),
            contains('parse'),
            contains('extract'),
          ));
        }
      });
    });

    group('Data Consistency and Validation', () {
      test('should produce consistent transaction objects', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50 #morning';
        final results = <dynamic>[];
        
        // Parse the same text multiple times
        for (int i = 0; i < 5; i++) {
          final result = await service.parseTransaction(text);
          results.add(result);
        }
        
        // All results should have consistent amounts and types
        for (int i = 1; i < results.length; i++) {
          final current = results[i];
          final first = results[0];
          
          if (current.isSuccess && first.isSuccess) {
            expect(current.transaction.amount, equals(first.transaction.amount));
            expect(current.transaction.type, equals(first.transaction.type));
            expect(current.transaction.currencyCode, equals(first.transaction.currencyCode));
          }
        }
      });

      test('should validate transaction completeness', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Restaurant dinner \$45.99 #food #dinner';
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess || result.requiresUserInput) {
          final transaction = result.transaction;
          
          // Validate required fields
          expect(transaction.id, isNotEmpty);
          expect(transaction.amount, greaterThan(0.0));
          expect(TransactionType.values, contains(transaction.type));
          expect(transaction.date, isNotNull);
          expect(transaction.description, isNotEmpty);
          expect(transaction.currencyCode, isNotEmpty);
          
          // Validate optional fields are properly initialized
          expect(transaction.tags, isA<List<String>>());
          expect(transaction.categoryId, isNotEmpty);
        }
      });
    });

    group('Storage Integration', () {
      test('should persist and retrieve learned categories across sessions', () async {
        const vendorText = 'Local pizza place \$15.99';
        const categoryId = 'pizza';

        // First session
        {
          final service = await MlKitParserService.getInstance(mockStorage);
          await service.learnCategory(vendorText, categoryId);
        }

        // Simulate new session by creating new service instance
        // (In practice, this would be a new app session)
        {
          final newService = await MlKitParserService.getInstance(mockStorage);
          final result = await newService.parseTransaction(vendorText);
          
          if (result.isSuccess) {
            expect(result.transaction.categoryId, equals(categoryId));
          }
        }
      });

      test('should handle storage errors gracefully', () async {
        // Test with corrupted storage data
        await mockStorage.setString('learned_categories', 'invalid json');
        
        final service = await MlKitParserService.getInstance(mockStorage);
        
        expect(() async {
          await service.parseTransaction('Coffee \$5.50');
        }, returnsNormally);
      });
    });

    group('Reported Issues - Integration Tests', () {
      test('should handle amount parsing edge cases across both parsers', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        final testCases = {
          'Spent ¥2500 on dinner': 2500.0,
          'Cost ¥3500 for shopping': 3500.0,
          'Paid ¥10000 for rent': 10000.0,
          'Bought ¥5000 groceries': 5000.0,
          'Expense ¥7500 for travel': 7500.0,
        };

        for (final entry in testCases.entries) {
          final mlkitResult = await mlkitService.parseTransaction(entry.key);
          final fallbackResult = await fallbackService.parseTransaction(entry.key);

          // At least one should succeed
          expect(mlkitResult.isSuccess || fallbackResult.isSuccess, isTrue,
                 reason: 'At least one parser should handle: ${entry.key}');

          // Check the successful result
          final successfulResult = mlkitResult.isSuccess ? mlkitResult : fallbackResult;
          expect(successfulResult.transaction.amount, equals(entry.value),
                 reason: 'Amount parsing failed for: ${entry.key}');
        }
      });

      test('should handle currency context detection across both parsers', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        final testCases = {
          'Beijing restaurant ¥45.50': 'CNY',
          'Tokyo sushi ¥1200': 'JPY',
          'Shanghai taxi ¥25': 'CNY',
          'Kyoto temple ¥500': 'JPY',
          'China trip ¥500': 'CNY',
          'Japan travel ¥2000': 'JPY',
        };

        for (final entry in testCases.entries) {
          final mlkitResult = await mlkitService.parseTransaction(entry.key);
          final fallbackResult = await fallbackService.parseTransaction(entry.key);

          // Check successful results for correct currency
          if (mlkitResult.isSuccess || mlkitResult.requiresUserInput) {
            expect(mlkitResult.transaction.currencyCode, equals(entry.value),
                   reason: 'MLKit currency detection failed for: ${entry.key}');
          }
          if (fallbackResult.isSuccess || fallbackResult.requiresUserInput) {
            expect(fallbackResult.transaction.currencyCode, equals(entry.value),
                   reason: 'Fallback currency detection failed for: ${entry.key}');
          }
        }
      });

      test('should handle negative number detection across both parsers', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        final testCases = {
          '-¥500 for toys': TransactionType.expense,
          '-\$25.50 coffee': TransactionType.expense,
          '-€100 for shopping': TransactionType.expense,
          '-£75.99 for dinner': TransactionType.expense,
        };

        for (final entry in testCases.entries) {
          final mlkitResult = await mlkitService.parseTransaction(entry.key);
          final fallbackResult = await fallbackService.parseTransaction(entry.key);

          // Check both parsers handle negative amounts correctly
          if (mlkitResult.isSuccess || mlkitResult.requiresUserInput) {
            expect(mlkitResult.transaction.type, equals(entry.value),
                   reason: 'MLKit negative detection failed for: ${entry.key}');
            expect(mlkitResult.transaction.amount, greaterThan(0),
                   reason: 'MLKit amount should be positive for: ${entry.key}');
          }
          if (fallbackResult.isSuccess || fallbackResult.requiresUserInput) {
            expect(fallbackResult.transaction.type, equals(entry.value),
                   reason: 'Fallback negative detection failed for: ${entry.key}');
            expect(fallbackResult.transaction.amount, greaterThan(0),
                   reason: 'Fallback amount should be positive for: ${entry.key}');
          }
        }
      });

      test('should handle category keyword conflicts across both parsers', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        final testCases = {
          'grocery shopping ¥100': 'food',
          'food shopping ¥50': 'food',
          'clothes shopping ¥200': 'shopping',
          'electronics shopping ¥500': 'shopping',
        };

        for (final entry in testCases.entries) {
          final mlkitResult = await mlkitService.parseTransaction(entry.key);
          final fallbackResult = await fallbackService.parseTransaction(entry.key);

          // Check category resolution in successful results
          if (mlkitResult.isSuccess) {
            expect(mlkitResult.transaction.categoryId, equals(entry.value),
                   reason: 'MLKit category conflict resolution failed for: ${entry.key}');
          }
          if (fallbackResult.isSuccess) {
            expect(fallbackResult.transaction.categoryId, equals(entry.value),
                   reason: 'Fallback category conflict resolution failed for: ${entry.key}');
          }
        }
      });

      test('should demonstrate enhanced fallback logic', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        final testCases = [
          'Spent ¥2500 on dinner',
          'grocery shopping ¥100',
          'Beijing restaurant ¥45.50',
          '-¥500 for toys',
          'clothes shopping ¥200',
        ];

        for (final text in testCases) {
          final mlkitResult = await mlkitService.parseTransaction(text);

          // If MLKit fails, fallback should work
          if (mlkitResult.hasError) {
            final fallbackResult = await fallbackService.parseTransaction(text);
            expect(fallbackResult.isSuccess || fallbackResult.requiresUserInput, isTrue,
                   reason: 'Enhanced fallback should handle: $text');
          }
        }
      });
    });

    group('Currency and Localization', () {
      test('should handle various default currencies', () async {
        final currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY'];

        for (final currency in currencies) {
          await mockStorage.saveDefaultCurrency(currency);
          final service = await MlKitParserService.getInstance(mockStorage);

          const text = 'Spent 25.50 on lunch'; // No currency symbol
          final result = await service.parseTransaction(text);

          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(currency));
          }
        }
      });

      test('should handle currency formatting edge cases', () async {
        final service = await MlKitParserService.getInstance(mockStorage);

        final edgeCases = [
          'Price: 1,234.56 dollars', // Written currency
          'Cost €1.000,50', // European decimal notation
          'Amount: \$1,000.00 USD', // Redundant currency info
          'Total ¥100,000', // Large amount with commas
        ];

        for (final text in edgeCases) {
          final result = await service.parseTransaction(text);

          expect(result, isNotNull, reason: 'Should handle currency edge case: $text');
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, greaterThan(0.0),
                   reason: 'Should extract amount from: $text');
          }
        }
      });
    });

    group('Currency Bug Fix Integration Tests', () {
      test('should use default currency instead of hardcoded USD', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        final testCases = [
          {'text': '1000 food', 'expectedCurrency': 'VND'},
          {'text': '50000 transport', 'expectedCurrency': 'VND'},
          {'text': '25000 shopping', 'expectedCurrency': 'VND'},
        ];

        for (final testCase in testCases) {
          final result = await mlkitService.parseTransaction(testCase['text'] as String);

          // Should use VND currency, not hardcoded USD
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Should use default currency VND for: ${testCase['text']}');
        }
      });

      test('should respect explicit currency over default currency', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        final testCases = [
          {'text': '\$1000 food', 'expectedCurrency': 'USD'},
          {'text': '€500 transport', 'expectedCurrency': 'EUR'},
          {'text': '1000 USD shopping', 'expectedCurrency': 'USD'},
          {'text': '500 EUR dining', 'expectedCurrency': 'EUR'},
        ];

        for (final testCase in testCases) {
          final result = await mlkitService.parseTransaction(testCase['text'] as String);

          // Should use explicit currency, not default VND
          expect(result.transaction.currencyCode, equals(testCase['expectedCurrency']),
              reason: 'Should use explicit currency for: ${testCase['text']}');
        }
      });

      test('should work across all parser services', () async {
        // Test with both MLKit and Fallback parsers
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        // Set default currency to EUR
        await mockStorage.saveDefaultCurrency('EUR');

        final testText = '100 lunch';

        // Both parsers should use EUR as default currency
        final mlkitResult = await mlkitService.parseTransaction(testText);
        final fallbackResult = await fallbackService.parseTransaction(testText);

        expect(mlkitResult.transaction.currencyCode, equals('EUR'),
            reason: 'MLKit parser should use default currency EUR');
        expect(fallbackResult.transaction.currencyCode, equals('EUR'),
            reason: 'Fallback parser should use default currency EUR');
      });
    });

    group('Number Abbreviation Integration Tests', () {
      test('should parse abbreviations across all parser services', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        final testCases = [
          {'text': '100k food', 'expectedAmount': 100000.0},
          {'text': '2.5M salary', 'expectedAmount': 2500000.0},
          {'text': '1.2B investment', 'expectedAmount': 1200000000.0},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final expectedAmount = testCase['expectedAmount'] as double;

          // Test MLKit parser
          final mlkitResult = await mlkitService.parseTransaction(text);
          expect(mlkitResult.transaction.amount, equals(expectedAmount),
              reason: 'MLKit parser failed for: $text');

          // Test Fallback parser
          final fallbackResult = await fallbackService.parseTransaction(text);
          expect(fallbackResult.transaction.amount, equals(expectedAmount),
              reason: 'Fallback parser failed for: $text');
        }
      });

      test('should handle abbreviations with currency symbols', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        final testCases = [
          {'text': '\$100k shopping', 'expectedAmount': 100000.0, 'expectedCurrency': 'USD'},
          {'text': '€2.5M bonus', 'expectedAmount': 2500000.0, 'expectedCurrency': 'EUR'},
          {'text': '£1.5k transport', 'expectedAmount': 1500.0, 'expectedCurrency': 'GBP'},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final expectedAmount = testCase['expectedAmount'] as double;
          final expectedCurrency = testCase['expectedCurrency'] as String;

          // Test MLKit parser
          final mlkitResult = await mlkitService.parseTransaction(text);
          expect(mlkitResult.transaction.amount, equals(expectedAmount),
              reason: 'MLKit amount parsing failed for: $text');
          expect(mlkitResult.transaction.currencyCode, equals(expectedCurrency),
              reason: 'MLKit currency detection failed for: $text');

          // Test Fallback parser
          final fallbackResult = await fallbackService.parseTransaction(text);
          expect(fallbackResult.transaction.amount, equals(expectedAmount),
              reason: 'Fallback amount parsing failed for: $text');
          expect(fallbackResult.transaction.currencyCode, equals(expectedCurrency),
              reason: 'Fallback currency detection failed for: $text');
        }
      });

      test('should handle case variations in abbreviations', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);

        final testCases = [
          {'text': '100K food', 'expectedAmount': 100000.0},
          {'text': '2m salary', 'expectedAmount': 2000000.0},
          {'text': '1.5B investment', 'expectedAmount': 1500000000.0},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final expectedAmount = testCase['expectedAmount'] as double;

          final result = await mlkitService.parseTransaction(text);
          expect(result.transaction.amount, equals(expectedAmount),
              reason: 'Case-insensitive parsing failed for: $text');
        }
      });

      test('should maintain backward compatibility with non-abbreviated amounts', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);
        final fallbackService = FallbackParserService(mockStorage);

        final testCases = [
          {'text': '\$100.50 food', 'expectedAmount': 100.50},
          {'text': '1,500 shopping', 'expectedAmount': 1500.0},
          {'text': '250.75 transport', 'expectedAmount': 250.75},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final expectedAmount = testCase['expectedAmount'] as double;

          // Test both parsers for backward compatibility
          final mlkitResult = await mlkitService.parseTransaction(text);
          expect(mlkitResult.transaction.amount, equals(expectedAmount),
              reason: 'MLKit backward compatibility failed for: $text');

          final fallbackResult = await fallbackService.parseTransaction(text);
          expect(fallbackResult.transaction.amount, equals(expectedAmount),
              reason: 'Fallback backward compatibility failed for: $text');
        }
      });

      test('should handle complex scenarios with both fixes', () async {
        final mlkitService = await MlKitParserService.getInstance(mockStorage);

        // Set default currency to JPY
        await mockStorage.saveDefaultCurrency('JPY');

        final testCases = [
          // Abbreviation with default currency
          {'text': '100k food', 'expectedAmount': 100000.0, 'expectedCurrency': 'JPY'},
          // Abbreviation with explicit currency symbol
          {'text': '\$2.5M investment', 'expectedAmount': 2500000.0, 'expectedCurrency': 'USD'},
          // Abbreviation with explicit currency code
          {'text': '1.5B EUR bonus', 'expectedAmount': 1500000000.0, 'expectedCurrency': 'EUR'},
          // Regular amount with default currency
          {'text': '500 lunch', 'expectedAmount': 500.0, 'expectedCurrency': 'JPY'},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final expectedAmount = testCase['expectedAmount'] as double;
          final expectedCurrency = testCase['expectedCurrency'] as String;

          final result = await mlkitService.parseTransaction(text);
          expect(result.transaction.amount, equals(expectedAmount),
              reason: 'Amount parsing failed for: $text');
          expect(result.transaction.currencyCode, equals(expectedCurrency),
              reason: 'Currency detection failed for: $text');
        }
      });
    });
  });
}
