# Setup Guide

This guide provides detailed instructions for setting up the Money Lover Chat development environment.

## Prerequisites

### Required Software

1. **Flutter SDK**
   - Version: 3.0.0 or higher
   - [Download Flutter](https://flutter.dev/docs/get-started/install)

2. **Dart SDK**
   - Version: 3.0.0 or higher (included with Flutter)

3. **IDE**
   - [Android Studio](https://developer.android.com/studio) (recommended for Android development)
   - [Visual Studio Code](https://code.visualstudio.com/) with Flutter and Dart extensions
   - [Xcode](https://developer.apple.com/xcode/) (for macOS/iOS development)

4. **Git**
   - [Download Git](https://git-scm.com/downloads)

### Platform-specific Requirements

#### Android Development

1. **Java Development Kit (JDK)**
   - Version: 11 or higher
   - [Download JDK](https://www.oracle.com/java/technologies/javase-jdk11-downloads.html)

2. **Android SDK**
   - Installed via Android Studio

3. **Android Device or Emulator**
   - Physical device with USB debugging enabled
   - Emulator via Android Virtual Device (AVD) Manager

#### iOS Development (macOS only)

1. **Xcode**
   - Latest version from the Mac App Store

2. **CocoaPods**
   - Install via: `sudo gem install cocoapods`

## Environment Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd money_lover_chat
```

### 2. Install Dependencies

```bash
flutter pub get
```

### 3. Verify Setup

Run the Flutter doctor command to verify your setup:

```bash
flutter doctor
```

Address any issues reported by Flutter doctor before proceeding.

### 4. Configure IDE

#### Visual Studio Code

1. Install the Flutter and Dart extensions
2. Open the project folder
3. Configure Flutter SDK path in settings

#### Android Studio

1. Install the Flutter and Dart plugins
2. Open the project folder
3. Configure Flutter SDK path in settings

### 5. Run the Application

```bash
flutter run
```

## Troubleshooting

### Common Issues

1. **Flutter SDK not found**
   - Ensure Flutter is in your PATH
   - Verify the Flutter SDK path in your IDE settings

2. **Gradle build failures**
   - Try running `flutter clean` and then `flutter run`

3. **iOS build issues**
   - Run `pod install` in the `ios/` directory
   - Ensure Xcode is updated to the latest version

4. **Android SDK issues**
   - Verify Android SDK installation path
   - Install missing SDK components via SDK Manager

## Development Workflow

### Code Style

Follow the [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style) for consistent code formatting.

Run the formatter regularly:

```bash
flutter format .
```

### Testing

Run tests with:

```bash
flutter test
```

### Building for Release

#### Android

```bash
flutter build apk --release
# OR for app bundle
flutter build appbundle --release
```

#### iOS (macOS only)

```bash
flutter build ios --release
```

Then use Xcode to archive and distribute the app. 