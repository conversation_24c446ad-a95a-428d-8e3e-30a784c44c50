# Architecture Documentation

This document describes the architecture of the Money Lover Chat application, including its components, data flow, and key design patterns.

## Architectural Overview

The application follows a layered architecture pattern with Provider for state management:

```mermaid
graph TD
    UI[UI Layer - Screens & Widgets]
    BL[Business Logic Layer - Providers]
    SL[Service Layer]
    DL[Data Layer - Models & Storage]
    
    UI --> BL
    BL --> SL
    SL --> DL
    BL --> DL
```

## Component Diagram

The major components of the application and their interactions:

```mermaid
graph TD
    subgraph "Presentation Layer"
        CS[Categories Screen]
        ChS[Chat Screen]
        SS[Settings Screen]
        StS[Statistics Screen]
        AN[App Navigation]
    end
    
    subgraph "Business Logic Layer"
        TP[Transaction Provider]
        ThP[Theme Provider]
    end
    
    subgraph "Service Layer"
        subgraph "Parsing Module"
            MPS[MlKitParserService]
            FPS[FallbackParserService]
            CFS[CategoryFinderService]
            LCS[LearnedCategoryStorage]
        end
        STS[Storage Service]
        CUtils[CurrencyUtils]
        AR[Audio Recorder]
        FU[File Upload]
        IU[Image Upload]
        VR[Video Recorder]
    end
    
    subgraph "Data Layer"
        TM[Transaction Model]
        PR[ParseResult Model]
        CM[Category Model]
        SP[Shared Preferences]
    end
    
    ChS --> TP
    ChS --> MPS
    MPS --> FPS
    MPS --> CFS
    MPS --> PR
    CFS --> LCS
    LCS --> STS

    CS --> TP
    SS --> ThP
    SS --> STS
    StS --> TP
    
    TP --> STS
    TP --> TM
    TP --> CM
    ThP --> SP
    
    STS --> SP
```

## State Management

The application uses the Provider pattern for state management. This provides a simple and efficient way to manage and propagate state changes throughout the application.

```mermaid
graph TD
    subgraph "Widget Tree"
        RW[Root Widget - MultiProvider]
        ML[MoneyLoverChatApp]
        AN[AppNavigation]
        SC[Screen Components]
    end
    
    subgraph "Providers"
        TP[TransactionProvider]
        ThP[ThemeProvider]
        MPS_P[MlKitParserService Provider]
    end
    
    subgraph "Data Sources"
        STS[Storage Service]
        SP[Shared Preferences]
    end
    
    RW --> TP
    RW --> ThP
    RW --> MPS_P
    RW --> ML
    ML --> AN
    AN --> SC
    
    SC -- Consumer --> TP
    SC -- Consumer --> ThP
    SC -- Consumer --> MPS_P
    
    TP --> STS
    STS --> SP
    ThP --> SP
```

## Data Flow - Transaction Processing

The sequence of operations when a user enters a transaction via chat has been updated to a "soft fail" conversational flow.

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant MlKitParserService
    participant TransactionProvider
    
    User->>ChatScreen: Enters ambiguous text (e.g., "movie night 20")
    ChatScreen->>MlKitParserService: parseTransaction(text)
    MlKitParserService-->>ChatScreen: Return ParseResult(status=needsType)
    
    ChatScreen->>User: "What type of transaction is this?"
    ChatScreen->>User: Show Quick-Reply: [Expense], [Income]
    
    User->>ChatScreen: Taps [Expense]
    ChatScreen->>MlKitParserService: findCategory(text, type=Expense)
    
    alt Category Found
        MlKitParserService-->>ChatScreen: Return ParseResult(status=success)
        ChatScreen->>TransactionProvider: addTransactionFromChat(transaction)
        TransactionProvider-->>ChatScreen: Update State
        ChatScreen->>User: "✅ Transaction Saved"
    else Category Not Found
        MlKitParserService-->>ChatScreen: Return ParseResult(status=needsCategory)
        ChatScreen->>User: Show CategoryPickerDialog
        User->>ChatScreen: Selects "Entertainment"
        ChatScreen->>MlKitParserService: learnCategory(text, "entertainment")
        ChatScreen->>TransactionProvider: addTransactionFromChat(updatedTransaction)
        TransactionProvider-->>ChatScreen: Update State
        ChatScreen->>User: "✅ Transaction Saved"
        ChatScreen->>User: "💡 I'll remember that for next time."
    end
```

## File Structure and Responsibility

| Component | Primary File(s) | Responsibility |
|-----------|-----------------|----------------|
| UI Layer | `screens/*.dart`, `widgets/*.dart` | User interface and interaction |
| Navigation | `navigation/app_navigation.dart` | Screen routing and navigation |
| State Management | `models/transaction_model.dart`, `theme.dart` | App-wide state management |
| Services | `services/*.dart` | Business logic implementation |
| Parsing Module | `services/parser/*.dart` | Hybrid ML transaction parsing |
| Utilities | `utils/*.dart` | Helper functions (e.g., currency) |
| Data Models | `models/*.dart` | Data structure definitions |
| Storage | `services/storage_service.dart` | Data persistence |

## Design Patterns

- **Provider Pattern**: Used for state management across the application.
- **Service Layer Pattern**: Used to separate business logic from UI components.
- **Singleton Pattern**: Used for managing service instances like `MlKitParserService`.
- **Strategy Pattern**: The hybrid parser uses ML Kit as its primary strategy and falls back to a regex-based strategy.
- **Observer Pattern**: Implemented via Provider for reactive UI updates.
 