# Chat-Based Transaction Entry

This document explains the chat-based transaction entry feature, which is a core functionality of the Money Lover Chat application.

## Overview

The chat interface provides an intuitive, conversational way for users to enter financial transactions. Instead of filling out forms, users can type natural language statements like "Spent $25 on lunch today" or "200k vnd for travel," and the app will parse these into structured transaction data. The system is designed to be a helpful assistant, asking for clarification when input is ambiguous rather than failing.

## User Flow

The user flow is designed to handle ambiguity gracefully, turning potential errors into a simple, interactive conversation.

```mermaid
graph TD
    A[User opens Chat Screen] --> B[User types transaction text]
    B --> C{Parser Service Processes Text}
    C --> D{Amount & Type Clear?}
    D -- Yes --> E{Category Clear?}
    E -- Yes --> F[Transaction Saved]
    F --> G[Display Confirmation]
    E -- No --> H[Show Category Picker]
    H --> I[User Selects Category]
    I --> J[Transaction Saved with Learning]
    J --> K[Display Confirmation & Learning Message]
    D -- No --> L[Show Type Disambiguation]
    L --> M[User Selects Type via Quick Reply]
    M --> E
```

## Components Involved

### UI Components
- `ChatScreen`: Main interface for the chat interaction.
- `TransactionMessage`: Displays a saved transaction in the chat log.
- `CategoryPickerDialog`: A dialog for manual category selection.
- `QuickReplyWidget`: Renders interactive buttons for disambiguation.

### Business Logic
- `TransactionProvider`: Manages transaction and chat message state.
- `MlKitParserService`: The primary service that orchestrates parsing using a hybrid ML and regex approach.
- `CategoryFinderService`: Determines transaction category based on keywords and user's learned history.

### Data Storage
- `StorageService`: Persists transaction data, learned categories, and user settings (like default currency).

## Implementation Details

### The Hybrid Parsing System

The core of the feature is the `MlKitParserService`, which uses a multi-stage process:

1.  **ML Kit Entity Extraction**: The service first uses Google's on-device ML Kit to identify key entities like money (amount and currency) and dates from the user's text, regardless of language.
2.  **Fallback Parsing**: If ML Kit is unavailable or fails, the system seamlessly falls back to a custom `FallbackParserService`. This secondary parser uses localizable regex patterns loaded from JSON resource files to extract the same information.
3.  **Transaction Type Detection**: Both parsers attempt to determine if the transaction is an `expense`, `income`, or `loan` based on keywords.
4.  **Category Finding**: The remaining text is passed to the `CategoryFinderService`, which uses a "Keyword & Learn" strategy to assign a category. It first checks for keywords the user has taught it before falling back to a general keyword list.

### Conversational "Soft Fails"

The system is designed to never hit a dead end.
-   **If Transaction Type is Unclear**: The parser returns a `needsType` status. The `ChatScreen` then prompts the user with quick-reply buttons (e.g., "Expense", "Income") to resolve the ambiguity.
-   **If Category is Unclear**: The parser returns a `needsCategory` status. The `ChatScreen` shows the `CategoryPickerDialog` for the user to make a selection. The app then saves this choice to accelerate future categorization.

### Code Example

Here's a conceptual overview of how the `ChatScreen` handles a parser result.

```dart
// In ChatScreen's _sendMessage method
Future<void> _handleParseResult(ParseResult result) async {
  switch (result.status) {
    case ParseStatus.success:
      // Automatically save the transaction
      await provider.addTransactionFromChat(result.transaction, originalText);
      break;
    
    case ParseStatus.needsType:
      // Show quick replies to ask user for transaction type
      _showTypeDisambiguation(result);
      break;

    case ParseStatus.needsCategory:
      // Show a dialog to ask user for the category
      _showCategorySelection(result);
      break;

    case ParseStatus.failed:
      // Show a user-friendly error message
      _addSystemMessage(result.error ?? "I had trouble understanding that.");
      break;
  }
}
```

## Future Enhancements

1.  **Smarter Suggestions**: Predict categories and vendors as the user types.
2.  **Voice Input**: Add support for voice-to-text transaction entry.
3.  **Recurring Transactions**: Detect and automate the setup of recurring bills or income.
 