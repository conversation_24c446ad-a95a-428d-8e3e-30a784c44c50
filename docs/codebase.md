# Codebase Structure

This document provides a detailed overview of the Money Lover Chat application codebase organization.

## Project Overview

The application follows a layered architecture with clear separation of concerns:

- **Presentation Layer**: Screens and UI components
- **Business Logic Layer**: Providers for state management
- **Service Layer**: Encapsulates business logic, parsing, and data access
- **Data Layer**: Data models and persistence services

## Directory Structure

### Root Level

```
money_lover_chat/
├── android/          # Android-specific configuration
├── ios/              # iOS-specific configuration
├── lib/              # Main Dart code
├── assets/           # Application assets (e.g., localization files)
│   └── l10n/
│       └── en.json
├── pubspec.yaml      # Dependencies and project configuration
└── ...
```

### Main Application Code (lib/)

```
lib/
├── models/           # Data models
│   ├── transaction_model.dart
│   └── parse_result.dart
├── services/         # Business logic and data services
│   ├── parser/       # Hybrid ML parsing module
│   │   ├── mlkit_parser_service.dart
│   │   ├── fallback_parser_service.dart
│   │   ├── category_finder_service.dart
│   │   └── ...
│   └── storage_service.dart
├── screens/          # UI screens
│   ├── chat_screen.dart
│   └── ...
├── widgets/          # Reusable UI components
│   ├── transaction_message.dart
│   ├── category_picker_dialog.dart
│   └── quick_reply_widget.dart
├── utils/            # Utility functions
│   └── currency_utils.dart
├── navigation/       # Navigation logic
│   └── app_navigation.dart
├── main.dart         # Application entry point
└── theme.dart        # App theme configuration
```

## Key Components

### Models (`lib/models/`)

- **transaction_model.dart**: Contains the core data models:
  - `Transaction`: Represents a financial transaction, including `amount` and `currencyCode`.
  - `Category`: Defines transaction categories.
  - `ChatMessage`: Represents a message in the chat, with support for quick replies.
  - `TransactionProvider`: Manages all transaction and chat message state using Provider.
- **parse_result.dart**:
  - `ParseResult`: A data transfer object from the parser service to the UI.
  - `ParseStatus`: An enum (`success`, `needsCategory`, `needsType`, `failed`) indicating the result of a parsing attempt.

### Services (`lib/services/`)

- **storage_service.dart**: Manages local data persistence using `shared_preferences`.
- **parser/mlkit_parser_service.dart**: The primary service for parsing user input. It orchestrates the hybrid system, using on-device ML Kit for entity extraction and falling back to a regex-based parser if needed.
- **parser/fallback_parser_service.dart**: A regex-based parser that serves as a safety net. It is designed to be localizable by loading patterns from external JSON files.
- **parser/category_finder_service.dart**: A service responsible for determining a transaction's category using a "Keyword & Learn" strategy.

### Utilities (`lib/utils/`)

- **currency_utils.dart**: Provides helper functions for formatting currency amounts and handling currency codes and symbols.

### Screens (`lib/screens/`)

- **chat_screen.dart**: The main chat interface for entering transactions. It interacts with the `MlKitParserService` and handles the conversational "soft fail" flows (type disambiguation and category selection).
- **settings_screen.dart**: Allows users to configure app settings, including the default currency.

### Widgets (`lib/widgets/`)

- **transaction_message.dart**: A widget to display a saved transaction within the chat history.
- **category_picker_dialog.dart**: A dialog for manually selecting a category when the parser cannot determine one automatically.
- **quick_reply_widget.dart**: A widget that displays interactive buttons in a chat message, used for transaction type disambiguation.
 