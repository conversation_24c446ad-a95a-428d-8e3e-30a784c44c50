import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../models/transaction_model.dart';
import '../models/parse_result.dart';
import '../services/parser/mlkit_parser_service.dart';
import '../services/parser/learned_association_service.dart';
import '../services/storage_service.dart';
import '../widgets/transaction_message.dart';
import '../widgets/transaction_edit_dialog.dart';
import '../widgets/category_picker_dialog.dart';
import '../widgets/quick_reply_widget.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  MlKitParserService? _parserService;
  LearnedAssociationService? _learnedAssociationService;
  final Uuid _uuid = Uuid();
  late AnimationController _animationController;
  bool _initialLoading = true;

  // State management for pending type selection
  ParseResult? _pendingTypeSelection;
  String? _pendingOriginalText;

  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Initialize ML Kit parser service
    _initializeParserService();

    // Add welcome message if this is the first time
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      
      // Add listener to scroll to bottom when messages change
      provider.addListener(_onProviderChanged);
      
      // Hide the initial loading indicator after a short delay
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() {
            _initialLoading = false;
          });
          
          // Scroll to bottom after loading is done
          _scrollToBottom();
        }
      });
      
      if (provider.messages.isEmpty) {
        _addSystemMessage('👋 Welcome to Money Lover Chat! Tell me about your financial transactions and I\'ll help you keep track of them.\n\nTry saying something like "Spent \$25 on dinner" or "Got \$1500 salary today".');
      }
      
      // Setup scroll controller to load more messages when scrolling to top
      _scrollController.addListener(_scrollListener);
    });
  }

  void _initializeParserService() async {
    try {
      print('Initializing parser service...');
      final storageService = StorageService();
      await storageService.init();
      _parserService = await MlKitParserService.getInstance(storageService);
      _learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
      print('Parser service initialized successfully');
    } catch (e) {
      print('Parser service initialization failed: $e');
      // Don't show error to user - the service will handle fallback internally
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Using basic parsing mode (ML Kit not available)'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _animationController.dispose();
    
    // Remove provider listener
    Provider.of<TransactionProvider>(context, listen: false).removeListener(_onProviderChanged);
    
    super.dispose();
  }
  
  void _scrollListener() {
    if (_scrollController.position.pixels <= _scrollController.position.minScrollExtent + 50) {
      final provider = Provider.of<TransactionProvider>(context, listen: false);
      
      // Save current position and content size
      final currentPosition = _scrollController.position.pixels;
      final currentContentSize = _scrollController.position.maxScrollExtent;
      
      provider.loadMoreMessages().then((_) {
        // Wait for layout to update with new content
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            // Calculate new position to maintain relative scroll position
            final newContentSize = _scrollController.position.maxScrollExtent;
            final newPosition = newContentSize - currentContentSize + currentPosition;
            
            if (newPosition > 0) {
              _scrollController.jumpTo(newPosition);
            }
          }
        });
      });
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Add user message
    _addUserMessage(text);

    // Clear input field
    _messageController.clear();

    // Parse transaction
    if (_parserService != null) {
      try {
        final parseResult = await _parserService!.parseTransaction(text);

        // Handle different parse statuses
        switch (parseResult.status) {
          case ParseStatus.success:
            // Automatically save the transaction
            await provider.addTransactionFromChat(parseResult.transaction, text);
            _scrollToBottom();
            break;

          case ParseStatus.needsCategory:
            // Show category picker dialog
            await _handleCategorySelection(parseResult, text);
            break;

          case ParseStatus.needsType:
            // Show quick reply for transaction type selection
            await _handleTypeSelection(parseResult, text);
            break;

          case ParseStatus.failed:
            // Parsing failed - show error message
            _addSystemMessage(
              parseResult.error ??
              "I couldn't detect a transaction in your message. Try using formats like "
              "\"spent \$50 on groceries\" or \"received \$1000 salary\".",
            );
            break;
        }
      } catch (e) {
        _addSystemMessage(
          "Sorry, there was an error processing your message. Please try again.",
        );
      }
    } else {
      // Parser service not initialized
      _addSystemMessage(
        "Parser service is not ready yet. Please try again in a moment.",
      );
    }
  }

  Future<void> _handleTypeSelection(ParseResult parseResult, String originalText) async {
    // Store pending transaction for type selection
    _pendingTypeSelection = parseResult;
    _pendingOriginalText = originalText;

    // Create system message with quick replies for transaction type
    final quickReplyId = _uuid.v4();
    final message = ChatMessage.systemWithQuickReplies(
      id: _uuid.v4(),
      text: 'I see ${_formatCurrency(parseResult.transaction.amount, parseResult.transaction.currencyCode)} for "${parseResult.transaction.description}". What type of transaction is this?',
      timestamp: DateTime.now(),
      quickReplies: const ['Expense', 'Income', 'Cancel'],
      quickReplyId: quickReplyId,
    );

    final provider = Provider.of<TransactionProvider>(context, listen: false);
    await provider.addMessage(message);
    _scrollToBottom();
  }

  Future<void> _handleCategorySelection(ParseResult parseResult, String originalText) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Show category picker dialog
    final selectedCategoryId = await showCategoryPickerDialog(
      context: context,
      transactionType: parseResult.transaction.type,
      initialCategoryId: parseResult.transaction.categoryId,
    );

    if (selectedCategoryId != null) {
      // Update transaction with selected category
      final updatedTransaction = parseResult.transaction.copyWith(
        categoryId: selectedCategoryId,
      );

      // Save the learning association (both type and category if available)
      if (_learnedAssociationService != null) {
        await _learnedAssociationService!.learn(
          originalText,
          type: updatedTransaction.type,
          categoryId: selectedCategoryId,
        );
      }

      // Save the transaction
      await provider.addTransactionFromChat(updatedTransaction, originalText);

      // Add learning confirmation message
      final category = provider.getCategoryById(selectedCategoryId);
      if (category != null) {
        // Extract key words from the original text for learning message
        final keywords = _extractKeywords(originalText);
        if (keywords.isNotEmpty) {
          _addSystemMessage(
            '💡 I\'ll remember to categorize "${keywords}" as ${category.name} for you next time.',
          );
        }
      }

      _scrollToBottom();
    } else {
      // User cancelled - save with original category
      await provider.addTransactionFromChat(parseResult.transaction, originalText);
      _scrollToBottom();
    }
  }

  void _addUserMessage(String text) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final message = ChatMessage.user(
      id: _uuid.v4(),
      text: text,
      timestamp: DateTime.now(),
    );
    provider.addMessage(message);

    // Always scroll to bottom when a new message is added
    _scrollToBottom();
  }

  void _addSystemMessage(String text, {String? transactionId}) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final message = ChatMessage.system(
      id: _uuid.v4(),
      text: text,
      timestamp: DateTime.now(),
      associatedTransactionId: transactionId,
    );
    provider.addMessage(message);

    // Always scroll to bottom when a new message is added
    _scrollToBottom();
  }

  // Handle quick reply selection
  Future<void> _onQuickReplySelected(String quickReplyId, String selectedOption) async {
    if (_pendingTypeSelection != null && _pendingOriginalText != null) {
      await _handleTypeSelectionResponse(selectedOption);
    }
  }

  // Handle transaction type selection response
  Future<void> _handleTypeSelectionResponse(String selectedType) async {
    if (_pendingTypeSelection == null || _pendingOriginalText == null) return;

    if (selectedType == 'Cancel') {
      // User cancelled
      _addSystemMessage('Transaction cancelled.');
      _pendingTypeSelection = null;
      _pendingOriginalText = null;
      return;
    }

    // Convert selected type to TransactionType enum
    TransactionType? transactionType;
    switch (selectedType) {
      case 'Expense':
        transactionType = TransactionType.expense;
        break;
      case 'Income':
        transactionType = TransactionType.income;
        break;
      default:
        transactionType = TransactionType.expense;
    }

    // Learn the type association
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(_pendingOriginalText!, type: transactionType);
    }

    // Update the pending transaction with the selected type
    final updatedTransaction = _pendingTypeSelection!.transaction.copyWith(
      type: transactionType,
    );

    // Now proceed to category detection
    // For now, we'll assume category selection is needed and let the user choose
    final parseResult = ParseResult.needsCategory(updatedTransaction);
    await _handleCategorySelection(parseResult, _pendingOriginalText!);

    // Clear pending state
    _pendingTypeSelection = null;
    _pendingOriginalText = null;
  }

  // Helper method to format currency
  String _formatCurrency(double amount, String currencyCode) {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    return provider.formatCurrency(amount, currencyCode);
  }

  // Helper method to extract keywords from text for learning messages
  String _extractKeywords(String text) {
    // Remove common words and extract meaningful keywords
    final words = text.toLowerCase().split(RegExp(r'\s+'));
    final stopWords = {'for', 'on', 'at', 'in', 'to', 'from', 'with', 'the', 'a', 'an', 'and', 'or', 'but'};
    final keywords = words.where((word) =>
      word.length > 2 &&
      !stopWords.contains(word) &&
      !RegExp(r'^\d+$').hasMatch(word) && // Not just numbers
      !RegExp(r'^[\$€£¥₹₽₩₱₫฿₺₪]+$').hasMatch(word) // Not just currency symbols
    ).take(3).join(' ');

    return keywords;
  }

  Future<void> _showEditDialog(Transaction transaction) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    
    final result = await showDialog<Transaction>(
      context: context,
      builder: (context) => TransactionEditDialog(transaction: transaction),
    );
    
    if (result != null) {
      // Update the transaction
      await provider.updateTransaction(result);
      
      // Show success message
      final category = provider.getCategoryById(result.categoryId);
      final categoryName = category?.name ?? 'Other';
      
      _addSystemMessage(
        '✅ Transaction updated: ${provider.formatCurrency(result.amount)} for ${result.description}, Category: $categoryName',
        transactionId: result.id,
      );
    }
  }

  Future<void> _showDeleteConfirmation(String transactionId) async {
    final provider = Provider.of<TransactionProvider>(context, listen: false);
    final transaction = provider.transactions.firstWhere((t) => t.id == transactionId);
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text(
          'Are you sure you want to delete this transaction: ${provider.formatCurrency(transaction.amount)} for ${transaction.description}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (result == true) {
      await provider.deleteTransaction(transactionId);
      
      _addSystemMessage(
        '🗑️ Transaction deleted successfully.',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to properly handle keep alive
    super.build(context);
    
    final theme = Theme.of(context);
    final provider = Provider.of<TransactionProvider>(context);
    final messages = provider.messages;
    final isLoading = provider.isLoading;

    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.chat_bubble),
            SizedBox(width: 8),
            Text('Money Lover Chat'),
          ],
        ),
        scrolledUnderElevation: 3,
        shadowColor: theme.colorScheme.shadow.withOpacity(0.1),
      ),
      body: Column(
        children: [
          // Loading indicator for pagination
          if (isLoading && !_initialLoading)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              color: theme.colorScheme.primaryContainer.withOpacity(0.3),
              child: const Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 16, 
                      height: 16, 
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 8),
                    Text('Loading older messages...'),
                  ],
                ),
              ),
            ),
            
          // Messages list
          Expanded(
            child: _initialLoading
                ? _buildInitialLoadingState(theme)
                : (messages.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          final isLastMessage = index == messages.length - 1;
                          
                          // For animation of the last message
                          if (isLastMessage) {
                            _animationController.forward(from: 0.0);
                          }
                          
                          return SlideTransition(
                            position: Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
                              CurvedAnimation(
                                parent: _animationController,
                                curve: Curves.easeOut,
                              ),
                            ),
                            child: FadeTransition(
                              opacity: _animationController,
                              child: _buildMessageBubble(message, theme),
                            ),
                          );
                        },
                      )
                ),
          ),
          
          // Message input
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 3,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: SafeArea(
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: 'Type a transaction...',
                        prefixIcon: const Icon(Icons.text_fields),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () => _messageController.clear(),
                          tooltip: 'Clear',
                        ),
                      ),
                      minLines: 1,
                      maxLines: 3,
                      textCapitalization: TextCapitalization.sentences,
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  FloatingActionButton(
                    onPressed: _sendMessage,
                    mini: true,
                    elevation: 0,
                    child: const Icon(Icons.send),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Start a conversation',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Try saying something like "Spent \$25 on dinner" or "Got \$1500 salary today"',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.withOpacity(0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
              strokeWidth: 4,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Loading messages...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please wait while we fetch your conversation history',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message, ThemeData theme) {
    final isUserMessage = message.type == ChatMessageType.user;
    final hasQuickReplies = message.type == ChatMessageType.systemWithQuickReplies;
    final provider = Provider.of<TransactionProvider>(context, listen: false);

    // Check if this message is associated with a transaction
    Transaction? associatedTransaction;
    if (message.associatedTransactionId != null) {
      try {
        associatedTransaction = provider.transactions.firstWhere(
          (t) => t.id == message.associatedTransactionId,
        );
      } catch (e) {
        // Transaction might have been deleted
        associatedTransaction = null;
      }
    }

    // Get the category if there's an associated transaction
    Category? category;
    if (associatedTransaction != null) {
      category = provider.getCategoryById(associatedTransaction.categoryId);
    }
    
    return Align(
      alignment: isUserMessage ? Alignment.centerRight : Alignment.centerLeft,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isUserMessage 
                ? theme.brightness == Brightness.light 
                    ? theme.colorScheme.primary 
                    : theme.colorScheme.primary.withOpacity(0.8)
                : theme.brightness == Brightness.light 
                    ? Colors.grey.shade200 
                    : Colors.grey.shade800,
            borderRadius: BorderRadius.circular(18).copyWith(
              bottomRight: isUserMessage ? const Radius.circular(0) : null,
              bottomLeft: !isUserMessage ? const Radius.circular(0) : null,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Message text
              Text(
                message.text,
                style: TextStyle(
                  color: isUserMessage 
                      ? Colors.white 
                      : theme.brightness == Brightness.light 
                          ? Colors.black87 
                          : Colors.white,
                  fontSize: 16,
                ),
              ),
              
              // Transaction message if available
              if (associatedTransaction != null && category != null)
                TransactionMessage(
                  transaction: associatedTransaction,
                  category: category,
                  onEdit: _showEditDialog,
                  onDelete: _showDeleteConfirmation,
                ),

              // Quick replies if available
              if (hasQuickReplies && message.quickReplies != null && message.quickReplies!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: QuickReplyWidget(
                    replyOptions: message.quickReplies!,
                    onReplySelected: (selectedOption) {
                      _onQuickReplySelected(message.quickReplyId ?? '', selectedOption);
                    },
                  ),
                ),

              // Timestamp
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  _formatTime(message.timestamp),
                  style: TextStyle(
                    fontSize: 10,
                    color: isUserMessage
                        ? Colors.white.withOpacity(0.7)
                        : Colors.grey,
                  ),
                  textAlign: isUserMessage ? TextAlign.right : TextAlign.left,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    final hours = dateTime.hour.toString().padLeft(2, '0');
    final minutes = dateTime.minute.toString().padLeft(2, '0');
    final time = '$hours:$minutes';
    
    if (date == today) {
      return 'Today, $time';
    } else if (date == today.subtract(const Duration(days: 1))) {
      return 'Yesterday, $time';
    } else {
      final month = dateTime.month.toString().padLeft(2, '0');
      final day = dateTime.day.toString().padLeft(2, '0');
      return '$day/$month, $time';
    }
  }

  // Listen to transaction provider changes to scroll to bottom when messages are added
  void _onProviderChanged() {
    // Check if we need to scroll (only if we're near the bottom already)
    if (_scrollController.hasClients) {
      final position = _scrollController.position;
      final maxScroll = position.maxScrollExtent;
      final currentScroll = position.pixels;
      
      // If we're near the bottom (within 200 pixels), scroll all the way down
      if (maxScroll - currentScroll < 200) {
        _scrollToBottom();
      }
    }
  }
}