import 'dart:ui';
import 'package:uuid/uuid.dart';
import '../../models/transaction_model.dart';
import '../../models/parse_result.dart';
import '../../models/localization_data.dart';
import '../../utils/currency_utils.dart';
import '../storage_service.dart';
import '../localization_service.dart';
import 'category_finder_service.dart';
import 'learned_association_service.dart';

/// Fallback transaction parser that uses regex-based parsing when ML Kit is not available
class FallbackParserService {
  final CategoryFinderService _categoryFinder;
  final StorageService _storageService;
  final LocalizationService _localizationService;
  LearnedAssociationService? _learnedAssociationService;
  final Uuid _uuid = const Uuid();

  FallbackParserService(
    StorageService storageService, {
    LocalizationService? localizationService,
  })  : _categoryFinder = CategoryFinderService(storageService),
        _storageService = storageService,
        _localizationService = localizationService ?? LocalizationService.instance {
    _initializeLearnedAssociationService(storageService);
  }

  /// Initialize the learned association service
  void _initializeLearnedAssociationService(StorageService storageService) async {
    try {
      _learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
    } catch (e) {
      print('Failed to initialize learned association service in FallbackParserService: $e');
    }
  }

  /// Parse transaction using regex-based approach with localization support
  Future<ParseResult> parseTransaction(String text, {Locale? locale}) async {
    try {
      // Get current locale or use default
      final currentLocale = locale ?? const Locale('en');

      // Load localization patterns
      final localizationData = await _localizationService.getPatternsForLocale(currentLocale);

      // Normalize text for easier parsing
      final normalizedText = text.toLowerCase().trim();

      // Try to extract amount and currency first to detect negative amounts
      final amountResult = _extractAmount(normalizedText, localizationData);
      if (amountResult == null || amountResult['amount'] == null) {
        return ParseResult.failed(
          _createFallbackTransaction(text),
          'Could not extract amount from text'
        );
      }

      final amount = amountResult['amount'] as double;
      final isNegativeAmount = amountResult['isNegative'] as bool? ?? false;
      String currencyCode = amountResult['currency'] as String? ?? await _storageService.getDefaultCurrency();

      // Try to detect transaction type with negative amount information
      final detectedType = _detectTransactionType(normalizedText, localizationData, isNegativeAmount: isNegativeAmount);

      // If transaction type is unclear, return needsType status
      if (detectedType == null) {
        // Create partial transaction with default expense type for type disambiguation
        final partialTransaction = Transaction(
          id: _uuid.v4(),
          amount: amount,
          type: TransactionType.expense, // Default type, will be updated by user selection
          categoryId: 'unknown', // Use placeholder for unknown categories
          date: DateTime.now(),
          description: _createDescription(text),
          tags: _extractTags(text),
          currencyCode: currencyCode,
        );

        return ParseResult.needsType(partialTransaction);
      }

      // Find category using the category finder service
      final categoryId = await _categoryFinder.findCategory(text, detectedType);

      // Create the transaction - use 'unknown' placeholder when no category found
      final transaction = Transaction(
        id: _uuid.v4(),
        amount: amount,
        type: detectedType,
        categoryId: categoryId ?? 'unknown', // Use placeholder for unknown categories
        date: DateTime.now(),
        description: _createDescription(text),
        tags: _extractTags(text),
        currencyCode: currencyCode,
      );

      // Return result indicating if category selection is needed
      if (categoryId == null) {
        return ParseResult.needsCategory(transaction);
      } else {
        return ParseResult.success(transaction);
      }

    } catch (e) {
      return ParseResult.failed(
        _createFallbackTransaction(text),
        'Parsing failed: $e'
      );
    }
  }

  /// Learn a category association for future use
  Future<void> learnCategory(String text, String categoryId) async {
    // Use new unified service if available
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(text, categoryId: categoryId);
    } else {
      // Fall back to category finder for backward compatibility
      await _categoryFinder.learnCategory(text, categoryId);
    }
  }

  /// Detect transaction type from text using localization data
  TransactionType? _detectTransactionType(String text, LocalizationData localizationData, {bool isNegativeAmount = false}) {
    // Check for negative sign or minus at the beginning which indicates expense
    if (RegExp(r'^\s*-').hasMatch(text) || isNegativeAmount) {
      return TransactionType.expense;
    }

    // Build dynamic regex patterns from localization data
    final incomePattern = _buildKeywordPattern(localizationData.incomeKeywords);
    final loanPattern = _buildKeywordPattern(localizationData.loanKeywords);
    final expensePattern = _buildKeywordPattern(localizationData.expenseKeywords);
    final currencyPattern = _buildKeywordPattern(localizationData.currencySymbols);

    // Income patterns (check these first as they're more specific)
    if (incomePattern.hasMatch(text)) {
      return TransactionType.income;
    }

    // Loan patterns
    if (loanPattern.hasMatch(text)) {
      return TransactionType.loan;
    }

    // Expense patterns (more general, check after specific income/loan patterns)
    if (expensePattern.hasMatch(text)) {
      return TransactionType.expense;
    }

    // Special case: 'for' keyword typically indicates expense unless preceded by income keyword
    final forPattern = localizationData.specialPatterns['for_keyword'];
    final incomeBeforeForPattern = localizationData.specialPatterns['income_before_for'];

    if (forPattern != null && RegExp(forPattern).hasMatch(text)) {
      if (incomeBeforeForPattern == null || !RegExp(incomeBeforeForPattern).hasMatch(text)) {
        return TransactionType.expense;
      }
    }

    // Default to expense if contains currency symbols
    if (currencyPattern.hasMatch(text)) {
      return TransactionType.expense;
    }

    return null;
  }

  /// Build a regex pattern from a list of keywords
  RegExp _buildKeywordPattern(List<String> keywords) {
    if (keywords.isEmpty) {
      return RegExp(r'(?!)'); // Never matches
    }

    // Escape special regex characters and join with OR
    final escapedKeywords = keywords.map((keyword) => RegExp.escape(keyword)).join('|');
    return RegExp('($escapedKeywords)', caseSensitive: false);
  }
  
  /// Extract amount from text using localization data
  Map<String, dynamic>? _extractAmount(String text, LocalizationData localizationData) {
    // Handle negative amount pattern first (like -500$ for toys)
    bool isNegative = false;
    String processedText = text;

    if (text.startsWith('-')) {
      isNegative = true;
      // Remove the leading minus for easier parsing
      processedText = text.substring(1).trim();
    }

    // Extract positive amount from processed text
    final result = _extractPositiveAmount(processedText, localizationData);
    if (result != null) {
      // Add negative flag to the result
      result['isNegative'] = isNegative;
    }

    return result;
  }
  
  /// Helper to extract positive amount values from text using localization data
  Map<String, dynamic>? _extractPositiveAmount(String text, LocalizationData localizationData) {
    // Build currency symbols pattern from localization data
    final currencySymbolsPattern = localizationData.currencySymbols.map((s) => RegExp.escape(s)).join('|');

    // Build regex pattern that respects localization separators
    final decimalSep = RegExp.escape(localizationData.decimalSeparator);
    final thousandsSep = RegExp.escape(localizationData.thousandsSeparator);

    // Create flexible amount regex that handles different separator configurations
    final amountRegex = RegExp(
      r'(' + currencySymbolsPattern + r')?\s?(\d+(?:' + thousandsSep + r'\d{3})*(?:' + decimalSep + r'\d{1,2})?)\s?(?:dollars|USD|euros?|EUR|pounds?|GBP|yen|JPY|yuan|CNY|rupees?|INR|rubles?|RUB|won|KRW|pesos?|MXN|PHP|dong|VND|baht|THB|lira|TRY|shekel|ILS|reais?|BRL|SGD|HKD|AUD|CAD|NZD|' + currencySymbolsPattern + r')?'
    );
    final match = amountRegex.firstMatch(text);

    if (match != null) {
      final currencySymbol = match.group(1);
      final amountString = match.group(2)!;

      // Convert amount string to double respecting localization separators
      final normalizedAmount = _normalizeAmountString(amountString, localizationData);
      final amount = double.tryParse(normalizedAmount);

      if (amount != null) {
        String? currency = _extractCurrency(text, localizationData);
        if (currency == null && currencySymbol != null) {
          currency = CurrencyUtils.symbolToCurrencyCode(currencySymbol, context: text);
        }

        return {
          'amount': amount,
          'currency': currency,
        };
      }
    }

    return null;
  }

  /// Normalize amount string by converting localized separators to standard format
  String _normalizeAmountString(String amountString, LocalizationData localizationData) {
    String normalized = amountString;

    // If thousands separator is different from comma, replace it with comma
    if (localizationData.thousandsSeparator != ',') {
      normalized = normalized.replaceAll(localizationData.thousandsSeparator, ',');
    }

    // If decimal separator is different from dot, replace it with dot
    if (localizationData.decimalSeparator != '.') {
      normalized = normalized.replaceAll(localizationData.decimalSeparator, '.');
    }

    // Remove commas for final parsing (standard approach)
    normalized = normalized.replaceAll(',', '');

    return normalized;
  }

  /// Extract currency information from text using localization data
  String? _extractCurrency(String text, LocalizationData localizationData) {
    // Check for currency symbols first with context-aware detection using localization data
    final currencySymbolsPattern = localizationData.currencySymbols.map((s) => RegExp.escape(s)).join('|');
    final symbolRegex = RegExp('($currencySymbolsPattern)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }

    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }

    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// Create description from text
  String _createDescription(String text) {
    return text.trim();
  }

  /// Extract tags from text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);
    
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }
    
    return tags;
  }

  /// Create fallback transaction when parsing fails
  Transaction _createFallbackTransaction(String text, {double? amount}) {
    return Transaction(
      id: _uuid.v4(),
      amount: amount ?? 0.0,
      type: TransactionType.expense,
      categoryId: 'unknown', // Use placeholder for unknown categories
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: 'USD',
    );
  }
}
