import '../../models/transaction_model.dart';
import '../storage_service.dart';
import 'learned_category_storage.dart';
import 'learned_association_service.dart';
import 'category_keyword_map.dart';

/// Service for finding transaction categories using keyword matching and learned associations
class CategoryFinderService {
  final LearnedCategoryStorage _learnedStorage;
  LearnedAssociationService? _learnedAssociationService;

  CategoryFinderService(StorageService storageService)
      : _learnedStorage = LearnedCategoryStorage(storageService) {
    _initializeLearnedAssociationService(storageService);
  }

  /// Initialize the learned association service
  void _initializeLearnedAssociationService(StorageService storageService) async {
    try {
      _learnedAssociationService = await LearnedAssociationService.getInstance(storageService);
    } catch (e) {
      print('Failed to initialize learned association service in CategoryFinderService: $e');
    }
  }

  /// Find category for the given text and transaction type
  /// Returns null if no category can be determined (triggering user selection)
  Future<String?> findCategory(String remainingText, TransactionType type) async {
    if (remainingText.trim().isEmpty) return null;

    // First check new unified learned associations
    if (_learnedAssociationService != null) {
      final learnedAssociation = await _learnedAssociationService!.getAssociation(remainingText);
      if (learnedAssociation?.categoryId != null) {
        return learnedAssociation!.categoryId;
      }
    }

    // Fall back to legacy learned storage for backward compatibility
    final learnedCategory = await _learnedStorage.getLearnedCategory(remainingText);
    if (learnedCategory != null) {
      return learnedCategory;
    }

    // Fall back to keyword matching
    final keywordCategory = findCategoryByKeywords(remainingText);
    if (keywordCategory != null) {
      return keywordCategory;
    }

    // No category found
    return null;
  }

  /// Save a user's category selection for future learning
  Future<void> learnCategory(String text, String categoryId) async {
    // Use new unified service if available
    if (_learnedAssociationService != null) {
      await _learnedAssociationService!.learn(text, categoryId: categoryId);
    } else {
      // Fall back to legacy storage for backward compatibility
      await _learnedStorage.saveLearnedCategory(text, categoryId);
    }
  }

  /// Get all learned associations for debugging
  Future<Map<String, String>> getAllLearnedCategories() async {
    return await _learnedStorage.getAllLearnedCategories();
  }

  /// Clear all learned data
  Future<void> clearLearnedData() async {
    await _learnedStorage.clearLearnedData();
  }

  /// Export learned data for debugging
  Future<String?> exportLearnedData() async {
    return await _learnedStorage.exportLearnedData();
  }

  /// Get available category IDs for keyword matching
  List<String> getAvailableCategoryIds() {
    return getAllCategoryIds();
  }

  /// Get keywords for a specific category
  List<String> getKeywordsForCategory(String categoryId) {
    return categoryKeywords[categoryId] ?? [];
  }
}
