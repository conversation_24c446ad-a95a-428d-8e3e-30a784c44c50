import 'dart:convert';
import '../storage_service.dart';

/// Service for persisting user-learned category associations
///
/// @deprecated This class is deprecated and will be removed in a future version.
/// Use LearnedAssociationService instead, which provides unified learning for both
/// transaction types and categories with improved data structure and migration support.
///
/// Migration: Data from this service is automatically migrated to LearnedAssociationService
/// when the new service is first initialized.
@Deprecated('Use LearnedAssociationService instead')
class LearnedCategoryStorage {
  static const String _storageKey = 'learned_categories';
  final StorageService _storageService;

  LearnedCategoryStorage(this._storageService);

  /// Retrieve a learned category for the given text
  ///
  /// @deprecated Use LearnedAssociationService.getAssociation() instead
  @Deprecated('Use LearnedAssociationService.getAssociation() instead')
  Future<String?> getLearnedCategory(String text) async {
    try {
      final normalizedText = _normalizeText(text);
      final storedData = _storageService.getString(_storageKey);
      
      if (storedData == null) return null;
      
      final Map<String, dynamic> learnedMap = jsonDecode(storedData);
      
      // First try exact match
      if (learnedMap.containsKey(normalizedText)) {
        return learnedMap[normalizedText] as String;
      }
      
      // Then try to find a partial match with stored vendor names
      final extractedVendor = _extractVendorName(normalizedText);
      if (extractedVendor != null && learnedMap.containsKey(extractedVendor)) {
        return learnedMap[extractedVendor] as String;
      }
      
      // Finally, check if any stored pattern is contained in the text or vice versa
      for (final entry in learnedMap.entries) {
        final storedPattern = entry.key;
        final categoryId = entry.value as String;
        
        // Check both directions and also word-level matches
        if (_isPartialMatch(normalizedText, storedPattern)) {
          return categoryId;
        }
      }
      
      return null;
    } catch (e) {
      // Log error if needed, but don't throw to avoid breaking the parsing flow
      return null;
    }
  }

  /// Save a learned category association
  ///
  /// @deprecated Use LearnedAssociationService.learn() instead
  @Deprecated('Use LearnedAssociationService.learn() instead')
  Future<void> saveLearnedCategory(String text, String categoryId) async {
    try {
      final normalizedText = _normalizeText(text);
      final vendorName = _extractVendorName(normalizedText);
      final keyToStore = vendorName ?? normalizedText;
      
      final storedData = _storageService.getString(_storageKey);
      Map<String, dynamic> learnedMap = {};
      
      if (storedData != null) {
        learnedMap = jsonDecode(storedData);
      }
      
      learnedMap[keyToStore] = categoryId;
      
      final updatedData = jsonEncode(learnedMap);
      await _storageService.setString(_storageKey, updatedData);
    } catch (e) {
      // Log error if needed, but don't throw to avoid breaking the saving flow
      rethrow;
    }
  }

  /// Get all learned associations for debugging
  Future<Map<String, String>> getAllLearnedCategories() async {
    try {
      final storedData = _storageService.getString(_storageKey);
      
      if (storedData == null) return {};
      
      final Map<String, dynamic> learnedMap = jsonDecode(storedData);
      return learnedMap.cast<String, String>();
    } catch (e) {
      return {};
    }
  }

  /// Clear all learned data
  Future<void> clearLearnedData() async {
    await _storageService.remove(_storageKey);
  }

  /// Export learned data as JSON string for debugging
  Future<String?> exportLearnedData() async {
    return _storageService.getString(_storageKey);
  }

  /// Normalize text for consistent lookup
  String _normalizeText(String text) {
    return text
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'[^\w\s]'), '') // Remove special chars
        .replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace
  }

  /// Extract vendor name or key phrase from transaction description
  String? _extractVendorName(String text) {
    // Remove common transaction prefixes and suffixes
    final cleanText = text
        .replaceAll(RegExp(r'^(spent|paid|buy|bought|purchase|dinner|lunch|breakfast)\s+(at|in|from)?\s*', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+(for|on|at|in)\s+.*$', caseSensitive: false), '')
        .replaceAll(RegExp(r'\s+(restaurant|coffee|shop|store|market|cafe)$', caseSensitive: false), '')
        .trim();
    
    // If the cleaned text is meaningful, use it
    if (cleanText.isNotEmpty && cleanText.length > 2) {
      return cleanText;
    }
    
    // Try to extract business name patterns (look for capitalized words or brands)
    final words = text.split(RegExp(r'\s+'));
    
    // Look for common business name patterns
    for (int i = 0; i < words.length; i++) {
      final word = words[i].toLowerCase();
      
      // Skip common transaction words
      if (['spent', 'paid', 'buy', 'bought', 'purchase', 'at', 'in', 'from', 'for', 'on', 'dinner', 'lunch', 'breakfast'].contains(word)) {
        continue;
      }
      
      // Try to build a business name from this position
      final businessWords = <String>[];
      for (int j = i; j < words.length && businessWords.length < 4; j++) {
        final currentWord = words[j].toLowerCase();
        
        // Stop at prepositions that usually end business names, but continue for key business words
        if (['for', 'on', 'with', 'and'].contains(currentWord) && businessWords.isNotEmpty) {
          break;
        }
        
        // Skip common suffixes if they're not core to the business name
        if (['restaurant', 'coffee', 'shop', 'store', 'market', 'cafe'].contains(currentWord) && businessWords.length >= 2) {
          break;
        }
        
        businessWords.add(currentWord);
      }
      
      if (businessWords.isNotEmpty) {
        final businessName = businessWords.join(' ');
        if (businessName.length > 2 && businessName.length < 30) {
          return businessName;
        }
      }
    }
    
    return null;
  }

  /// Check if two texts match partially using word-level comparison
  bool _isPartialMatch(String text1, String text2) {
    final words1 = text1.split(' ').where((w) => w.isNotEmpty).toSet();
    final words2 = text2.split(' ').where((w) => w.isNotEmpty).toSet();
    
    // If either text has common words with the other, consider it a match
    if (words1.intersection(words2).isNotEmpty) {
      return true;
    }
    
    // Also check substring matches for shorter patterns
    if (text1.length < text2.length && text2.contains(text1)) {
      return true;
    }
    if (text2.length < text1.length && text1.contains(text2)) {
      return true;
    }
    
    return false;
  }

  /// Migration helper: Get all learned data for migration to LearnedAssociationService
  ///
  /// This method is used by LearnedAssociationService to migrate existing data
  /// from the legacy storage format to the new unified format.
  ///
  /// @deprecated This method is only for migration purposes and will be removed
  /// when LearnedCategoryStorage is fully deprecated.
  @Deprecated('Only for migration to LearnedAssociationService')
  Future<Map<String, String>> getAllLearnedCategoriesForMigration() async {
    try {
      final storedData = _storageService.getString(_storageKey);
      if (storedData == null) return {};

      final Map<String, dynamic> learnedMap = jsonDecode(storedData);
      return learnedMap.cast<String, String>();
    } catch (e) {
      print('Error getting learned categories for migration: $e');
      return {};
    }
  }

  /// Migration helper: Clear legacy data after successful migration
  ///
  /// This method should only be called by LearnedAssociationService after
  /// successful migration of all data.
  ///
  /// @deprecated This method is only for migration purposes and will be removed
  /// when LearnedCategoryStorage is fully deprecated.
  @Deprecated('Only for migration to LearnedAssociationService')
  Future<void> clearLegacyDataAfterMigration() async {
    try {
      await _storageService.remove(_storageKey);
    } catch (e) {
      print('Error clearing legacy data after migration: $e');
    }
  }
}
