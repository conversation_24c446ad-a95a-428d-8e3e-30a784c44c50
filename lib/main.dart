import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'models/transaction_model.dart';
import 'services/storage_service.dart';
import 'services/parser/mlkit_parser_service.dart';
import 'navigation/app_navigation.dart';
import 'theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final storageService = StorageService();
  await storageService.init();
  
  // Initialize ML Kit parser service
  MlKitParserService? mlKitService;
  try {
    mlKitService = await MlKitParserService.getInstance(storageService);
  } catch (e) {
    // ML Kit initialization failed - app will use fallback parsing
    print('ML Kit initialization failed: $e');
  }
  
  runApp(MultiProvider(
    providers: [
      ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ChangeNotifierProvider(create: (_) => TransactionProvider(storageService)),
      // Provide the ML Kit service if it was successfully initialized
      if (mlKitService != null)
        Provider<MlKitParserService>.value(value: mlKitService),
    ],
    child: const MoneyLoverChatApp(),
  ));
}

class MoneyLoverChatApp extends StatelessWidget {
  const MoneyLoverChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return MaterialApp(
      title: 'Money Lover Chat',
      theme: themeProvider.themeData,
      debugShowCheckedModeBanner: false,
      home: const AppNavigation(),
    );
  }
}