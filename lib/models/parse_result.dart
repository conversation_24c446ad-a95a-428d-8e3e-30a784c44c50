import 'transaction_model.dart';

/// Enum representing the status of parsing results
enum ParseStatus {
  success,
  needsCategory,
  needsType,
  failed
}

/// Data transfer object for communicating parsing results between the ML Kit parser and the UI
class ParseResult {
  final Transaction transaction;
  final ParseStatus status;
  final String? error;

  const ParseResult({
    required this.transaction,
    required this.status,
    this.error,
  });

  /// Factory constructor for successful parsing without user input needed
  factory ParseResult.success(Transaction transaction) {
    return ParseResult(
      transaction: transaction,
      status: ParseStatus.success,
    );
  }

  /// Factory constructor for successful parsing but category selection needed
  factory ParseResult.needsCategory(Transaction transaction) {
    return ParseResult(
      transaction: transaction,
      status: ParseStatus.needsCategory,
    );
  }

  /// Factory constructor for successful parsing but transaction type selection needed
  factory ParseResult.needsType(Transaction partialTransaction) {
    return ParseResult(
      transaction: partialTransaction,
      status: ParseStatus.needsType,
    );
  }

  /// Factory constructor for failed parsing
  factory ParseResult.failed(Transaction fallbackTransaction, String error) {
    return ParseResult(
      transaction: fallbackTransaction,
      status: ParseStatus.failed,
      error: error,
    );
  }

  /// Helper method to check if parsing was successful
  bool get isSuccess => status == ParseStatus.success && error == null;

  /// Helper method to check if user input is required
  bool get requiresUserInput => status == ParseStatus.needsCategory || status == ParseStatus.needsType;

  /// Helper method to check if there was an error
  bool get hasError => error != null;

  /// Helper method to check if category selection is needed
  bool get needsCategorySelection => status == ParseStatus.needsCategory;

  /// Helper method to check if transaction type selection is needed
  bool get needsTypeSelection => status == ParseStatus.needsType;

  @override
  String toString() {
    return 'ParseResult{transaction: $transaction, status: $status, error: $error}';
  }
}
