# Money Lover Chat

A Flutter application for managing finances with transaction tracking, categorization, statistics, and a chat interface for transaction entry.

## Core Features

- Transaction recording and management
- Transaction categorization
- Financial statistics and visualization
- Chat interface for natural language transaction entry
- Theme customization

## Getting Started

### Requirements

- Flutter 3.0.0 or higher
- Dart SDK 3.0.0 or higher

### Setup

1. Clone the repository:
```bash
git clone <repository-url>
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the app:
```bash
flutter run
```

## Project Structure

- `lib/` - Main application code
  - `models/` - Data models
  - `services/` - Business logic and data services
  - `screens/` - UI screens
  - `navigation/` - Navigation logic
  - `*.dart` - Various utilities and helper classes

## Documentation

Detailed documentation is available in the `docs/` directory:

- [Codebase Structure](docs/codebase.md) - Detailed explanation of project organization
- [Architecture](docs/architecture.md) - Application architecture and design patterns
- [Setup Guide](docs/setup_guide.md) - Detailed environment setup

## Testing

```bash
flutter test
```

## Contributing

For contribution guidelines, please see [CONTRIBUTING.md](CONTRIBUTING.md). 